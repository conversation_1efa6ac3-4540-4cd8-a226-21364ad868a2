import org.jetbrains.compose.desktop.application.dsl.TargetFormat
import org.jetbrains.kotlin.gradle.ExperimentalKotlinGradlePluginApi
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.targets.js.dsl.ExperimentalWasmDsl
import org.jetbrains.kotlin.gradle.targets.js.webpack.KotlinWebpackConfig

plugins {
    alias(libs.plugins.kotlinMultiplatform)
    alias(libs.plugins.androidApplication)
    alias(libs.plugins.jetbrainsCompose)
    alias(libs.plugins.compose.compiler)
    id("client.js.target.library")
}

kotlin {
    @OptIn(ExperimentalWasmDsl::class)

    androidTarget {
        @OptIn(ExperimentalKotlinGradlePluginApi::class)
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_19)
        }
    }

    wasmJs {
        browser {
            commonWebpackConfig {
                devServer?.port = 8081
                // devServer?.static = mutableListOf("${layout.buildDirectory.asFile.get()}/processedResources/js/main")
                showProgress = true
            }
        }
        binaries.executable()
    }


    jvm("desktop")

//    listOf(
//        iosX64(),
//        iosArm64(),
//        iosSimulatorArm64()
//    ).forEach { iosTarget ->
//        iosTarget.binaries.framework {
//            baseName = "ComposeApp"
//            isStatic = true
//        }
//    }

    sourceSets {
        val desktopMain by getting

        androidMain.dependencies {
            implementation(compose.preview)
            implementation(libs.androidx.activity.compose)
        }
        commonMain.dependencies {
            implementation(project(":core-common"))
            implementation(project(":core-voice"))
            implementation(project(":core-http-client-common"))
            implementation(project(":core-monitoring-common"))
            implementation(compose.runtime)
            implementation(compose.foundation)
            implementation("org.jetbrains.compose.material3:material3:1.8.0-alpha03")
            implementation(compose.ui)
            implementation(compose.components.resources)
            implementation(compose.components.uiToolingPreview)
            implementation(projects.sharedData)
            implementation(projects.sharedDomain)
            implementation(projects.sharedPresentation)

            implementation(libs.kotlin.coroutines.core)
            implementation(libs.kotlin.datetime)

            implementation(libs.voyager.navigator)
            implementation(libs.voyager.screenmodel)
        }
        desktopMain.dependencies {
            implementation(compose.desktop.currentOs)
        }
    }
}

android {
    namespace = "com.ryinex.accountant"
    compileSdk = libs.versions.android.compileSdk.get().toInt()

    sourceSets["main"].manifest.srcFile("src/androidMain/AndroidManifest.xml")
    sourceSets["main"].res.srcDirs("src/androidMain/res")
    sourceSets["main"].resources.srcDirs("src/commonMain/resources")

    defaultConfig {
        applicationId = "com.ryinex.accountant"
        minSdk = libs.versions.android.minSdk.get().toInt()
        targetSdk = libs.versions.android.targetSdk.get().toInt()
        versionCode = 1
        versionName = "0.0.0"
    }

    signingConfigs {
        create("release") {
            val keyStoreFile = System.getProperty("SIGNING_KEYSTORE_FILE_PATH")
                ?: throw IllegalStateException("SIGNING_KEYSTORE_FILE_PATH is not set")
            val keyStoreStorePassword = System.getProperty("SIGNING_KEYSTORE_STORE_PASSWORD")
                ?: throw IllegalStateException("SIGNING_KEYSTORE_STORE_PASSWORD is not set")
            val keyStoreKeyAlias = System.getProperty("SIGNING_KEYSTORE_KEY_ALIAS")
                ?: throw IllegalStateException("SIGNING_KEYSTORE_KEY_ALIAS is not set")
            val keyStoreKeyPassword = System.getProperty("SIGNING_KEYSTORE_KEY_PASSWORD")
                ?: throw IllegalStateException("SIGNING_KEYSTORE_KEY_PASSWORD is not set")

            storeFile = rootProject.file(keyStoreFile)
            storePassword = keyStoreStorePassword
            keyAlias = keyStoreKeyAlias
            keyPassword = keyStoreKeyPassword
        }
    }

    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }
    buildTypes {
        getByName("release") {
            isMinifyEnabled = true
            signingConfig = signingConfigs.getByName("release")
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_19
        targetCompatibility = JavaVersion.VERSION_19
    }
    buildFeatures {
        compose = true
    }
    dependencies {
        debugImplementation(compose.uiTooling)
    }
}

compose.desktop {
    application {
        mainClass = "com.ryinex.composeApp.MainKt"

        nativeDistributions {
            targetFormats(TargetFormat.Dmg, TargetFormat.Msi, TargetFormat.Deb)
            packageName = "com.ryinex.accountant"
            packageVersion = "1.0.0"
        }
    }
}

jsDependencies {
    implementation("org.jetbrains.kotlinx:kotlinx-browser:0.2")
}