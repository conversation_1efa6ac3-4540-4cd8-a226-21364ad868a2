package com.ryinex.accountant.client.shared.data.customers.models

import com.ryinex.accountant.client.shared.data.users.models.User
import com.ryinex.accountant.client.shared.data.common.utilities.AppDoubleJson
import com.ryinex.accountant.client.shared.data.common.utilities.JavaSerializable
import kotlinx.serialization.Serializable

@Serializable
data class CustomerTransaction(
    val id: Long,
    val customerId: Long,
    val organizationId: Long,

    val amount: AppDoubleJson,
    val amountPaid: AppDoubleJson,
    val description: String,
    val transactionDate: String,

    val createdAt: String,
    val createdBy: Long,
    val updatedAt: String,
    val updatedBy: Long,
    val version: Long,

    val createdByUser: User
): JavaSerializable {

    val remaining: AppDoubleJson = amount - amountPaid
}