package com.ryinex.accountant.client.shared.data.customers.repositories

import com.ryinex.accountant.client.shared.data.customers.models.Customer
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.data.common.utilities.UserSettings
import core.http.client.HttpClient
import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put

class CustomersRepository(
    private val httpClient: HttpClient,
    private val apiBaseUrl: String,
    private val logger: Logger
) {
    suspend fun createCustomer(
        name: String,
        phoneNumber: String,
        secondaryPhoneNumber: String,
    ): Customer = logger.async {
        val url = "$apiBaseUrl/api/v1/customers"
        val body = buildJsonObject {
            put("name", name.trim())
            put("phoneNumber", phoneNumber.trim())
            put("secondaryPhoneNumber", secondaryPhoneNumber.trim())
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            headers = listOf(
                HttpHeader(
                    type = HttpHeader.Type.AUTHORIZATION,
                    value = UserSettings.userAccessToken.get()
                ),
                HttpHeader(
                    type = HttpHeader.Type.CONTENT_TYPE,
                    value = "application/json"
                )
            ),
            body = body
        )

        val response = httpClient.request(request).value(Customer.serializer())
        return@async response
    }

    suspend fun getCustomerById(customerId: Long): Customer = logger.async {
        val url = "$apiBaseUrl/api/v1/customers/$customerId"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(
                    type = HttpHeader.Type.AUTHORIZATION,
                    value = UserSettings.userAccessToken.get()
                )
            )
        )
        val response = httpClient.request(request).value(Customer.serializer())
        return@async response
    }

    suspend fun getCustomersOfOrganization(): List<Customer> = logger.async {
        val url = "$apiBaseUrl/api/v1/customers"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(
                    type = HttpHeader.Type.AUTHORIZATION,
                    value = UserSettings.userAccessToken.get()
                )
            )
        )
        val response = httpClient.request(request).value(ListSerializer(Customer.serializer()))

        return@async response.sortedBy { it.createdAt }
    }

    suspend fun edit(
        customerId: Long,
        name: String,
        phoneNumber: String,
        version: Long
    ) = logger.async {
        val url = "$apiBaseUrl/api/v1/customers/$customerId"
        val body = buildJsonObject {
            put("name", name.trim())
            put("phoneNumber", phoneNumber.trim())
            put("version", version)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.PATCH,
            headers = listOf(
                HttpHeader(
                    type = HttpHeader.Type.AUTHORIZATION,
                    value = UserSettings.userAccessToken.get()
                ),
                HttpHeader(
                    type = HttpHeader.Type.CONTENT_TYPE,
                    value = "application/json"
                )
            ),
            body = body
        )

        val response = httpClient.request(request).value(Customer.serializer())
        return@async response
    }
}