package com.ryinex.accountant.client.shared.data.beneficiaries.repositories

import com.ryinex.accountant.client.shared.data.beneficiaries.models.Beneficiary
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.data.common.utilities.UserSettings
import core.http.client.HttpClient
import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put

class BeneficiariesRepository(
    private val httpClient: HttpClient,
    private val apiBaseUrl: String,
    private val logger: Logger
) {
    suspend fun createBeneficiary(
        name: String,
        phoneNumber: String,
        secondaryPhoneNumber: String,
    ): Beneficiary = logger.async {
        val url = "$apiBaseUrl/api/v1/beneficiaries"
        val body = buildJsonObject {
            put("name", name.trim())
            put("phoneNumber", phoneNumber.trim())
            put("secondaryPhoneNumber", secondaryPhoneNumber.trim())
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            ),
            body = body
        )

        val response = httpClient.request(request).value(Beneficiary.serializer())
        return@async response
    }

    suspend fun getBeneficiaryById(beneficiaryId: Long): Beneficiary = logger.async {
        val url = "$apiBaseUrl/api/v1/beneficiaries/${beneficiaryId}"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get())
            )
        )

        val response = httpClient.request(request).value(Beneficiary.serializer())
        return@async response
    }

    suspend fun getBeneficiariesOfOrganization(): List<Beneficiary> = logger.async {
        val url = "$apiBaseUrl/api/v1/beneficiaries"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(
                    type = HttpHeader.Type.AUTHORIZATION,
                    value = UserSettings.userAccessToken.get()
                )
            )
        )
        val response = httpClient.request(request).value(ListSerializer(Beneficiary.serializer()))

        return@async response.sortedBy { it.createdAt }
    }

    suspend fun editBeneficiary(
        beneficiaryId: Long,
        name: String,
        phoneNumber: String,
        version: Long
    ): Beneficiary = logger.async {
        val url = "$apiBaseUrl/api/v1/beneficiaries/${beneficiaryId}"
        val body = buildJsonObject {
            put("name", name.trim())
            put("phoneNumber", phoneNumber.trim())
            put("version", version)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.PATCH,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            ),
            body = body
        )

        val response = httpClient.request(request).value(Beneficiary.serializer())
        return@async response
    }
}