package com.ryinex.accountant.client.shared.data.projects.models

import com.ryinex.accountant.client.shared.data.beneficiaries.models.Beneficiary
import com.ryinex.accountant.client.shared.data.beneficiaries.models.BeneficiaryTransaction
import com.ryinex.accountant.client.shared.data.terms.models.Term
import com.ryinex.accountant.client.shared.data.terms.models.TermsGroup
import com.ryinex.accountant.client.shared.data.common.utilities.JavaSerializable
import com.ryinex.accountant.client.shared.data.common.utilities.searchNormalized
import kotlinx.serialization.Serializable

@Serializable
data class ProjectExpense(
    val id: Long,
    val organizationId: Long,

    val projectId: Long,
    val term: Term?,
    val termsGroup: TermsGroup?,
    val beneficiary: Beneficiary?,
    val beneficiaryTransaction: BeneficiaryTransaction,
    val project: Project,

    val createdAt: String,
    val createdBy: Long,
    val updatedAt: String,
    val updatedBy: Long,
    val version: Long
): JavaSerializable {
    val searchText = "${beneficiary?.name ?: ""} ${term?.name ?: ""} ${beneficiaryTransaction.description} ${beneficiaryTransaction.createdByUser.name} ${project.name} ${termsGroup?.name ?: ""}".searchNormalized()
}