package com.ryinex.accountant.client.shared.data.organization.models

import com.ryinex.accountant.client.shared.data.users.models.User
import com.ryinex.accountant.client.shared.data.common.utilities.AppDouble
import com.ryinex.accountant.client.shared.data.common.utilities.JavaSerializable
import com.ryinex.accountant.client.shared.data.common.utilities.app
import kotlinx.serialization.Serializable

@Serializable
data class OrganizationCapitalTransactionDto(
    val id: Long,
    val organizationId: Long,
    val amount: Double,
    val description: String,
    val transactionDate: String,

    val createdAt: String,
    val createdBy: Long,
    val updatedAt: String,
    val updatedBy: Long,

    val createdByUser: User
) {
    fun toModel(): OrganizationCapitalTransaction {
        return OrganizationCapitalTransaction(
            id = id,
            amount = amount.app(),
            description = description,
            transactionDate = transactionDate,
            createdAt = createdAt,
            createdBy = createdBy,
            updatedAt = updatedAt,
            updatedBy = updatedBy,
            organizationId = organizationId,
            createdByUser = createdByUser
        )
    }
}

data class OrganizationCapitalTransaction(
    val id: Long,
    val organizationId: Long,

    val amount: AppDouble,
    val description: String,
    val transactionDate: String,

    val createdAt: String,
    val createdBy: Long,
    val updatedAt: String,
    val updatedBy: Long,

    val createdByUser: User
): JavaSerializable