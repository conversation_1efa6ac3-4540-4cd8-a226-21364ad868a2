package com.ryinex.accountant.client.shared.presentation.terms

import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.ic_edit
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.ModalBottomSheetDefaults
import androidx.compose.material3.SheetValue
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import cafe.adriel.voyager.navigator.LocalNavigator
import com.ryinex.accountant.client.shared.data.common.error.ErrorHandler
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppCard
import com.ryinex.accountant.client.shared.presentation.common.containers.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.containers.AppRow
import com.ryinex.accountant.client.shared.presentation.common.containers.adaptWidth
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.images.AppSupportImageViewButton
import com.ryinex.accountant.client.shared.presentation.common.texts.AppBodyText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabelText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSubSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTitleText
import com.ryinex.accountant.client.shared.data.terms.models.Term
import com.ryinex.accountant.client.shared.data.terms.models.TermsGroup
import com.ryinex.accountant.client.shared.data.terms.repositories.TermsGroupsRepository
import com.ryinex.accountant.client.shared.data.terms.repositories.TermsRepository
import com.ryinex.accountant.client.shared.domain.terms.ViewModel
import com.ryinex.accountant.client.shared.domain.terms.models.ScreenState
import com.ryinex.accountant.client.shared.presentation.common.radiobutton.AppLabeledRadioButton
import com.ryinex.accountant.client.shared.presentation.common.shadowBluish
import com.ryinex.accountant.client.shared.presentation.common.surface.AppBottomSheet
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSelectableAutoCompleteTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTextField
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource


internal fun LazyStaggeredGridScope.TermsGroupsListSection(
    viewModel: ViewModel,
    state: ScreenState
) {

    state.terms.forEach { termsGroup ->
        item {
            TermsGroupCard(
                map = termsGroup,
                isEnabled = state.isGroupDetailsEnabled,
                groups = state.groups,
                onFinishAddEdit = { viewModel.refreshScreen() }
            )
        }
    }
}

@Composable
private fun TermsGroupCard(
    modifier: Modifier = Modifier,
    map: Map.Entry<TermsGroup, List<Term>>,
    groups: List<TermsGroup>,
    isEnabled: Boolean,
    onFinishAddEdit: () -> Unit
) {
    val navigator = LocalNavigator.current
    AppCard(
        modifier = modifier.adaptWidth().shadowBluish(),
    ) {
        AppColumn(
            modifier = Modifier.fillMaxWidth().padding(Dimensions.Paddings.medium.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            var showEditSheet by remember { mutableStateOf(false) }
            var showAddSheet by remember { mutableStateOf(false) }

            AppColumn(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.Top,
                alignment = Alignment.Start
            ) {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    arrangement = Arrangement.SpaceBetween,
                    alignment = Alignment.CenterVertically
                ) {
                    AppSubSectionTitleText(text = map.key.name, color = MaterialTheme.colorScheme.primary)

                    AppColumn(
                        arrangement = Arrangement.Top,
                        alignment = Alignment.Start
                    ) {
                        AppTextButton(
                            enabled = isEnabled,
                            onClick = { showEditSheet = true },
                            text = "تعديل"
                        )
                    }
                }

                if (map.key.description != map.key.name) {
                    AppBodyText(text = map.key.description, color = MaterialTheme.colorScheme.onBackground)
                }
            }

            HorizontalDivider()

            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.SpaceBetween,
                alignment = Alignment.CenterVertically
            ) {
                AppLabelText("البنود")

                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    AppTextButton(
                        enabled = isEnabled,
                        onClick = { showAddSheet = true },
                        text = "إضافة"
                    )
                }
            }

            for (term in map.value) {
                TermRow(
                    term = term,
                    isEnabled = isEnabled,
                    onEdit = onFinishAddEdit
                )
            }


            if (showEditSheet) {
                TermsGroupSheet(
                    termsGroup = map.key,
                    onDismissRequest = { showEditSheet = false },
                    onFinish = onFinishAddEdit
                )
            }

            if (showAddSheet) {
                TermSheet(
                    term = null,
                    initialTermsGroup = map.key,
                    groups = groups,
                    isCancelTermsGroupEnabled = false,
                    onDismissRequest = { showAddSheet = false },
                    onFinish = onFinishAddEdit
                )
            }
        }
    }
}

@Composable
private fun TermRow(
    modifier: Modifier = Modifier,
    term: Term,
    isEnabled: Boolean,
    onEdit: () -> Unit
) {
    var showEditSheet by remember { mutableStateOf(false) }

    AppRow(
        modifier = modifier.fillMaxWidth(),
        arrangement = Arrangement.SpaceBetween,
        alignment = Alignment.Bottom
    ) {
        AppColumn(
            modifier = Modifier.weight(1f),
            arrangement = Arrangement.Top,
            alignment = Alignment.Start
        ) {
            AppTitleText(
                text = term.name,
                color = MaterialTheme.colorScheme.onBackground
            )

            if (term.description != term.name) {
                AppBodyText(text = term.description)
            }
        }

        AppSupportImageViewButton(
            painter = painterResource(Res.drawable.ic_edit),
            iconOnly = true,
            enabled = isEnabled,
            onClick = { showEditSheet = true }
        )
    }

    if (showEditSheet) {
        TermSheet(
            term = term,
            initialTermsGroup = null,
            groups = emptyList(),
            isCancelTermsGroupEnabled = false,
            onDismissRequest = { showEditSheet = false },
            onFinish = onEdit
        )
    }
}


@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun AddTermSheet(
    modifier: Modifier = Modifier,
    termsGroup: TermsGroup?,
    groups: List<TermsGroup>,
    isCancelTermsGroupEnabled: Boolean,
    onFinish: () -> Unit,
    onDismissRequest: () -> Unit
) {
    val minNameLength = remember { 3 }
    val minDescriptionLength = remember { 3 }
    val scope = rememberCoroutineScope()
    var isLoading by remember { mutableStateOf(false) }

    AppBottomSheet(
        modifier = modifier,
        onDismissRequest = onDismissRequest
    ) {
        var name by remember { mutableStateOf("") }
        var description by remember { mutableStateOf("") }
        var termsGroupText by remember { mutableStateOf(termsGroup?.name ?: "") }
        var termsGroup by remember { mutableStateOf<TermsGroup?>(termsGroup) }

        var newTermsGroupText by remember { mutableStateOf("") }
        var newTermsGroupDescription by remember { mutableStateOf("") }

        var selectedRadioButton by remember { mutableStateOf(if (termsGroup != null) 1 else -1) }

        val isNameError by remember(name) { mutableStateOf(name.trim().length < minNameLength) }
        val isDescriptionError by remember(description) { mutableStateOf(description.trim().length < minDescriptionLength) }
        val isNewTermsGroupNameError by remember(newTermsGroupText) { mutableStateOf(newTermsGroupText.trim().length < minNameLength) }
        val isNewTermsGroupDescriptionError by remember(newTermsGroupDescription) {
            mutableStateOf(newTermsGroupDescription.trim().length < minDescriptionLength)
        }

        val isEnabled by remember(
            isNameError,
            isDescriptionError,
            isNewTermsGroupNameError,
            isNewTermsGroupDescriptionError,
            selectedRadioButton,
            termsGroup,
            isLoading
        ) {
            val isGroupError = when (selectedRadioButton) {
                0 -> isNewTermsGroupNameError || isNewTermsGroupDescriptionError
                1 -> termsGroup == null
                else -> true
            }
            mutableStateOf(!isNameError && !isDescriptionError && !isGroupError && !isLoading)
        }

        AppColumn(
            modifier = Modifier.fillMaxWidth().padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            AppSectionTitleText(
                modifier = Modifier,
                text = "إضافة بند أعمال",
            )

            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                alignment = Alignment.CenterVertically
            ) {
                AppTextButton(
                    modifier = Modifier,
                    text = "إلغاء",
                    enabled = true,
                    onClick = { if (!isLoading) onDismissRequest() }
                )

                AppTextButton(
                    modifier = Modifier,
                    text = "إضافة",
                    enabled = isEnabled,
                    onClick = {
                        scope.launch {
                            isLoading = true

                            if (selectedRadioButton == 0) {
                                var isError = false
                                createTermsGroup(
                                    name = newTermsGroupText,
                                    description = newTermsGroupDescription,
                                    onSuccess = { termsGroup = it },
                                    onError = {
                                        isLoading = false
                                        isError = true
                                    }
                                )
                                delay(1000)
                                if (isError) return@launch
                            }

                            createTerm(
                                name = name,
                                description = description,
                                groupId = termsGroup!!.id,
                                onSuccess = onFinish,
                                onError = {
                                    isLoading = false
                                    onDismissRequest()
                                }
                            )
                            isLoading = false
                            onDismissRequest()
                        }
                    },
                )
            }

            AppTextField(
                modifier = Modifier.fillMaxWidth(),
                text = name,
                hint = "الاسم",
                enabled = true,
                isError = isNameError,
                onValueChange = { name = it },
                errorText = "البند يجب أن يكون على الأقل $minNameLength أحرف"
            )

            AppTextField(
                modifier = Modifier.fillMaxWidth(),
                text = description,
                hint = "الوصف",
                enabled = true,
                isError = isDescriptionError,
                onValueChange = { description = it },
                errorText = "الوصف يجب أن يكون على الأقل $minDescriptionLength أحرف"
            )

            if (termsGroup == null) {
                AppLabeledRadioButton(
                    modifier = Modifier.fillMaxWidth(),
                    enabled = true,
                    label = "إنشاء مجموعة أعمال جديدة",
                    selected = selectedRadioButton == 0,
                    onClick = { selectedRadioButton = 0 },
                )
            }

            if (termsGroup == null) {
                AppLabeledRadioButton(
                    modifier = Modifier.fillMaxWidth(),
                    enabled = true,
                    label = "إختيار مجموعة أعمال",
                    selected = selectedRadioButton == 1,
                    onClick = { selectedRadioButton = 1 },
                )
            }

            if (selectedRadioButton == 0) {
                AppTextField(
                    modifier = Modifier.fillMaxWidth(),
                    text = newTermsGroupText,
                    hint = "الإسم",
                    enabled = true,
                    isError = isNewTermsGroupNameError,
                    errorText = "المجموعة يجب أن يكون على الأقل $minNameLength أحرف",
                    onValueChange = { newTermsGroupText = it },
                )

                AppTextField(
                    modifier = Modifier.fillMaxWidth(),
                    text = newTermsGroupDescription,
                    hint = "الوصف",
                    enabled = true,
                    isError = isNewTermsGroupDescriptionError,
                    errorText = "الوصف يجب أن يكون على الأقل $minDescriptionLength أحرف",
                    onValueChange = { newTermsGroupDescription = it },
                )
            }

            if (selectedRadioButton == 1 || termsGroup != null) {
                AppSelectableAutoCompleteTextField(
                    modifier = Modifier.fillMaxWidth(),
                    items = groups,
                    text = termsGroupText,
                    onValueChange = { termsGroupText = it },
                    hint = "مجموعة الأعمال",
                    enabled = true,
                    isError = false,
                    errorText = "",
                    selected = termsGroup,
                    onItemClick = { termsGroup = it },
                    displayMapper = { it.name },
                    searchMapper = { it.name },
                    onCancelSelected = { termsGroup = null },
                    isCancelSelectedEnabled = isCancelTermsGroupEnabled,
                    onEmptyItemClick = null
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun EditTermSheet(
    modifier: Modifier = Modifier,
    initialName: String,
    initialDescription: String,
    termId: Long?,
    version: Long?,
    isEditing: Boolean,
    onFinish: () -> Unit,
    onDismissRequest: () -> Unit
) {
    val minNameLength = remember { 3 }
    val minDescriptionLength = remember { 3 }
    val scope = rememberCoroutineScope()
    var isLoading by remember { mutableStateOf(false) }

    AppBottomSheet(
        modifier = modifier,
        onDismissRequest = onDismissRequest
    ) {
        var name by remember { mutableStateOf(initialName) }
        var description by remember { mutableStateOf(initialDescription) }

        val isNameError by remember(name) { mutableStateOf(name.trim().length < minNameLength) }
        val isDescriptionError by remember(description) { mutableStateOf(description.trim().length < minDescriptionLength) }

        val isEnabled by remember(isNameError, isDescriptionError, isLoading) {
            mutableStateOf(!isNameError && !isDescriptionError && !isLoading)
        }

        AppColumn(
            modifier = Modifier.fillMaxWidth().padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            AppSectionTitleText(
                modifier = Modifier,
                text = if (isEditing) initialName else "إضافة بند أعمال"
            )

            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                alignment = Alignment.CenterVertically
            ) {
                AppTextButton(
                    modifier = Modifier,
                    text = "إلغاء",
                    enabled = true,
                    onClick = { onDismissRequest() }
                )

                AppTextButton(
                    modifier = Modifier,
                    text = if (isEditing) "تعديل" else "إضافة",
                    enabled = isEnabled,
                    onClick = {
                        scope.launch {
                            isLoading = true

                            editTerm(
                                name = name,
                                description = description,
                                termId = termId!!,
                                version = version!!,
                                onError = { isLoading = false }
                            )

                            isLoading = false
                            onFinish()
                            onDismissRequest()
                        }
                    },
                )
            }

            AppTextField(
                modifier = Modifier.fillMaxWidth(),
                text = name,
                hint = "الاسم",
                enabled = true,
                isError = isNameError,
                onValueChange = { name = it },
                errorText = "البند يجب أن يكون على الأقل $minNameLength أحرف"
            )

            AppTextField(
                modifier = Modifier.fillMaxWidth(),
                text = description,
                hint = "الوصف",
                enabled = true,
                isError = isDescriptionError,
                onValueChange = { description = it },
                errorText = "الوصف يجب أن يكون على الأقل $minDescriptionLength أحرف"
            )
        }
    }
}

private suspend fun createTerm(
    groupId: Long,
    name: String,
    description: String,
    terms: TermsRepository = TempDI.terms,
    status: StatusRepository = TempDI.status,
    errorHandler: ErrorHandler = TempDI.errorHandler,
    onSuccess: () -> Unit,
    onError: () -> Unit
) {
    errorHandler.execute {
        val result = status.execute(
            loading = Message.fromString("جاري الإضافة ..."),
            success = Message.fromString("تم الإضافة بنجاح"),
            job = {
                terms.create(groupId = groupId, name = name, description = description)
                onSuccess()
            }
        )

        if (result is AsyncResult.Fail) {
            onError()
            throw result.error
        }
    }
}

private suspend fun createTermsGroup(
    name: String,
    description: String,
    termsGroups: TermsGroupsRepository = TempDI.termsGroups,
    status: StatusRepository = TempDI.status,
    errorHandler: ErrorHandler = TempDI.errorHandler,
    onSuccess: (TermsGroup) -> Unit,
    onError: () -> Unit
) {
    errorHandler.execute {
        val result = status.execute(
            loading = Message.fromString("جاري إنشاء مجموعة أعمال ..."),
            success = Message.fromString("تم إنشاء مجموعة أعمال بنجاح"),
            job = {
                onSuccess(termsGroups.create(name = name, description = description))
            }
        )

        if (result is AsyncResult.Fail) {
            onError()
            throw result.error
        }
    }
}

private suspend fun editTerm(
    name: String,
    description: String,
    termId: Long,
    version: Long,
    terms: TermsRepository = TempDI.terms,
    status: StatusRepository = TempDI.status,
    errorHandler: ErrorHandler = TempDI.errorHandler,
    onError: () -> Unit
) {
    errorHandler.execute {
        val result = status.execute(
            loading = Message.fromString("جاري تعديل بند أعمال ..."),
            success = Message.fromString("تم تعديل بند أعمال بنجاح"),
            job = {
                terms.edit(name = name, description = description, termId = termId, version = version)
            }
        )

        if (result is AsyncResult.Fail) {
            onError()
            throw result.error
        }
    }
}


@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun AddTermsGroupSheet(
    modifier: Modifier = Modifier,
    initialName: String,
    initialDescription: String,
    termsGroupId: Long?,
    version: Long?,
    isEditing: Boolean,
    onFinish: () -> Unit,
    onDismissRequest: () -> Unit
) {
    val minNameLength = remember { 3 }
    val minDescriptionLength = remember { 3 }
    val scope = rememberCoroutineScope()
    var isLoading by remember { mutableStateOf(false) }

    AppBottomSheet(
        modifier = modifier,
        onDismissRequest = onDismissRequest
    ) {
        var name by remember { mutableStateOf(initialName) }
        var description by remember { mutableStateOf(initialDescription) }

        val isNameError by remember(name) { mutableStateOf(name.trim().length < minNameLength) }
        val isDescriptionError by remember(description) { mutableStateOf(description.trim().length < minDescriptionLength) }

        val isEnabled by remember(isNameError, isDescriptionError, isLoading) {
            mutableStateOf(!isNameError && !isDescriptionError && !isLoading)
        }

        AppColumn(
            modifier = Modifier.fillMaxWidth().padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            AppSectionTitleText(
                modifier = Modifier,
                text = if (isEditing) initialName else "إضافة مجموعة بنود أعمال"
            )

            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                alignment = Alignment.CenterVertically
            ) {
                AppTextButton(
                    modifier = Modifier,
                    text = "إلغاء",
                    enabled = true,
                    onClick = { onDismissRequest() }
                )

                AppTextButton(
                    modifier = Modifier,
                    text = if (isEditing) "تعديل" else "إضافة",
                    enabled = isEnabled,
                    onClick = {
                        scope.launch {
                            isLoading = true

                            if (!isEditing) {
                                addTermsGroup(name = name, description = description, onError = { isLoading = false })
                            } else {
                                editTermsGroup(
                                    name = name,
                                    description = description,
                                    termsGroupId = termsGroupId!!,
                                    version = version!!,
                                    onError = { isLoading = false }
                                )
                            }

                            isLoading = false
                            onFinish()
                            onDismissRequest()
                        }
                    },
                )
            }

            AppTextField(
                modifier = Modifier.fillMaxWidth(),
                text = name,
                hint = "الاسم",
                enabled = true,
                isError = isNameError,
                onValueChange = { name = it },
                errorText = "المجموعة يجب أن يكون على الأقل $minNameLength أحرف"
            )

            AppTextField(
                modifier = Modifier.fillMaxWidth(),
                text = description,
                hint = "الوصف",
                enabled = true,
                isError = isDescriptionError,
                onValueChange = { description = it },
                errorText = "الوصف يجب أن يكون على الأقل $minDescriptionLength أحرف"
            )
        }
    }
}

private suspend fun addTermsGroup(
    name: String,
    description: String,
    status: StatusRepository = TempDI.status,
    errorHandler: ErrorHandler = TempDI.errorHandler,
    termsGroupRepository: TermsGroupsRepository = TempDI.termsGroups,
    onError: () -> Unit
) {
    errorHandler.execute {
        val result = status.execute(
            loading = Message.fromString("إضافة مجموعة بنود أعمال"),
            success = Message.fromString("تم إضافة مجموعة بنود أعمال"),
            job = {
                termsGroupRepository.create(name, description)
            }
        )

        if (result is AsyncResult.Fail) {
            onError()
            throw result.error
        }
    }
}

private suspend fun editTermsGroup(
    name: String,
    description: String,
    termsGroupId: Long,
    version: Long,
    status: StatusRepository = TempDI.status,
    errorHandler: ErrorHandler = TempDI.errorHandler,
    termsGroupRepository: TermsGroupsRepository = TempDI.termsGroups,
    onError: () -> Unit
) {
    errorHandler.execute {
        val result = status.execute(
            loading = Message.fromString("تعديل مجموعة الأعمال"),
            success = Message.fromString("تم تعديل مجموعة الأعمال"),
            job = {
                termsGroupRepository.edit(
                    name = name,
                    description = description,
                    termsGroupId = termsGroupId,
                    version = version
                )
            }
        )

        if (result is AsyncResult.Fail) {
            onError()
            throw result.error
        }
    }
}