package com.ryinex.accountant.client.shared.presentation.custody

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.data.custody.models.Custody
import com.ryinex.accountant.client.shared.data.users.models.User
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.containers.AppRow
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.state.BooleanState
import com.ryinex.accountant.client.shared.presentation.common.state.DerivedState
import com.ryinex.accountant.client.shared.presentation.common.state.MutableItemState
import com.ryinex.accountant.client.shared.presentation.common.state.StringState
import com.ryinex.accountant.client.shared.presentation.common.surface.AppBottomSheet
import com.ryinex.accountant.client.shared.presentation.common.texts.AppDateTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppMoneyTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSelectableAutoCompleteTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTextField
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.execute
import core.common.utilities.AppDouble
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock

private class StateHolder(
    val amount: StringState = StringState(""),
    val description: StringState = StringState(""),
    val user: MutableItemState<User?> = MutableItemState(null),
    val userText: StringState = StringState(""),
    val transactionDate: StringState = StringState(""),
    val isLoading: BooleanState = BooleanState(false)
) {
    private val minDescriptionLength = 3

    val isAmountError = DerivedState(true, amount.stream.map { 
        it.trim().isEmpty() || it.toDoubleOrNull() == null || it.toDouble() <= 0 
    })
    val isDescriptionError = DerivedState(true, description.stream.map { it.trim().length < minDescriptionLength })
    val isUserError = DerivedState(true, user.stream.map { it == null })
    val isTransactionDateError = DerivedState(true, transactionDate.stream.map { it.trim().isEmpty() })

    private val actionEnabledStream = com.ryinex.accountant.client.shared.data.common.utilities.combine(
        isAmountError.stream,
        isDescriptionError.stream,
        isUserError.stream,
        isTransactionDateError.stream,
        isLoading.stream
    ) { amountError, descriptionError, userError, dateError, loading ->
        !loading && !amountError && !descriptionError && !userError && !dateError
    }

    val actionEnabled = DerivedState(false, actionEnabledStream)

    fun fromCustody(custody: Custody) {
        amount.update(custody.amount.toString())
        description.update(custody.description)
        user.update(custody.user)
        userText.update(custody.user.name)
        transactionDate.update(custody.transactionDate)
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CustodySheet(
    custody: Custody? = null,
    users: List<User> = emptyList(),
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    val scope = rememberCoroutineScope()
    val state = remember { StateHolder() }

    LaunchedEffect(Unit) {
        if (custody != null) {
            state.fromCustody(custody)
        } else {
            // Set default date to today
            state.transactionDate.update(Clock.System.now().toString().substring(0, 10))
        }
    }

    AppBottomSheet(onDismissRequest = onDismissRequest) {
        AppLazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            SheetContent(
                state = state,
                custody = custody,
                users = users,
                onDismissRequest = onDismissRequest,
                onFinish = onFinish
            )
        }
    }
}

private fun LazyListScope.SheetContent(
    state: StateHolder,
    custody: Custody?,
    users: List<User>,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    item {
        val title = if (custody != null) "تعديل عهدة" else "إضافة عهدة"
        AppSectionTitleText(title)
    }

    item { ActionsRow(state = state, custody = custody, onDismissRequest = onDismissRequest, onFinish = onFinish) }

    item { AmountInput(state = state) }

    item { DescriptionInput(state = state) }

    item { UserInput(state = state, users = users) }

    item { TransactionDateInput(state = state) }
}

@Composable
private fun AmountInput(state: StateHolder) {
    AppMoneyTextField(
        modifier = Modifier.fillMaxWidth(),
        money = state.amount.value,
        hint = "المبلغ",
        enabled = !state.isLoading.value,
        isError = state.isAmountError.value,
        onValueChange = { state.amount.update(it) },
        errorText = "المبلغ يجب أن يكون أكبر من صفر"
    )
}

@Composable
private fun DescriptionInput(state: StateHolder) {
    AppTextField(
        modifier = Modifier.fillMaxWidth(),
        text = state.description.value,
        hint = "الوصف",
        enabled = !state.isLoading.value,
        isError = state.isDescriptionError.value,
        onValueChange = { state.description.update(it) },
        errorText = "الوصف يجب أن يكون على الأقل 3 أحرف"
    )
}

@Composable
private fun UserInput(state: StateHolder, users: List<User>) {
    AppSelectableAutoCompleteTextField(
        modifier = Modifier.fillMaxWidth(),
        items = users,
        text = state.userText.value,
        onValueChange = { state.userText.update(it) },
        hint = "المستخدم",
        enabled = !state.isLoading.value,
        isError = state.isUserError.value,
        errorText = "يجب اختيار مستخدم",
        selected = state.user.value,
        onItemClick = { 
            state.user.update(it)
            state.userText.update(it.name)
        },
        displayMapper = { it.name },
        searchMapper = { it.name },
        onCancelSelected = { 
            state.user.update(null)
            state.userText.update("")
        },
        isCancelSelectedEnabled = true,
        onEmptyItemClick = null
    )
}

@Composable
private fun TransactionDateInput(state: StateHolder) {
    AppDateTextField(
        modifier = Modifier.fillMaxWidth(),
        date = state.transactionDate.value,
        hint = "تاريخ المعاملة",
        enabled = !state.isLoading.value,
        isError = state.isTransactionDateError.value,
        onValueChange = { state.transactionDate.update(it) },
        errorText = "يجب إدخال تاريخ صحيح"
    )
}

@Composable
private fun ActionsRow(
    modifier: Modifier = Modifier,
    state: StateHolder,
    custody: Custody?,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    val scope = rememberCoroutineScope()

    AppRow(
        modifier = modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
        alignment = Alignment.CenterVertically
    ) {
        AppTextButton(
            modifier = Modifier,
            text = "إلغاء",
            enabled = !state.isLoading.value,
            onClick = onDismissRequest
        )

        AppTextButton(
            modifier = Modifier,
            text = if (custody != null) "تعديل" else "إضافة",
            enabled = state.actionEnabled.value,
            onClick = {
                scope.launch {
                    if (custody != null) {
                        updateCustody(state = state, custody = custody, onFinish = onFinish)
                    } else {
                        addCustody(state = state, onFinish = onFinish)
                    }
                    onDismissRequest()
                }
            }
        )
    }
}

private suspend fun addCustody(
    state: StateHolder,
    onFinish: () -> Unit
) {
    jobWrapper(
        state = state,
        loading = "جاري إضافة العهدة ...",
        success = "تم إضافة العهدة",
        job = { addCustodyJob(state = state, onFinish = onFinish) }
    )
}

private suspend fun addCustodyJob(
    state: StateHolder,
    onFinish: () -> Unit
) {
    TempDI.custody.create(
        userId = state.user.get()!!.id,
        amount = AppDouble(state.amount.get().toDouble()),
        description = state.description.get(),
        transactionDate = state.transactionDate.get()
    )

    onFinish()
}

private suspend fun updateCustody(
    state: StateHolder,
    custody: Custody,
    onFinish: () -> Unit
) {
    jobWrapper(
        state = state,
        loading = "جاري تعديل العهدة ...",
        success = "تم تعديل العهدة",
        job = { updateCustodyJob(custody = custody, state = state, onFinish = onFinish) }
    )
}

private suspend fun updateCustodyJob(
    custody: Custody,
    state: StateHolder,
    onFinish: () -> Unit
) {
    TempDI.custody.edit(
        custodyId = custody.id,
        userId = state.user.get()!!.id,
        amount = AppDouble(state.amount.get().toDouble()),
        description = state.description.get(),
        transactionDate = state.transactionDate.get(),
        version = custody.version
    )

    onFinish()
}

private suspend fun <T> jobWrapper(state: StateHolder, loading: String, success: String, job: suspend () -> T): T? {
    val status = TempDI.status
    val errorHolder = TempDI.errorHandler

    return errorHolder.execute {
        state.isLoading.update(true)

        val result = status.execute(
            loading = Message.fromString(loading),
            success = Message.fromString(success),
            job = job
        )

        state.isLoading.update(false)

        return@execute if (result is AsyncResult.Fail) throw result.error else result.result
    }
}
