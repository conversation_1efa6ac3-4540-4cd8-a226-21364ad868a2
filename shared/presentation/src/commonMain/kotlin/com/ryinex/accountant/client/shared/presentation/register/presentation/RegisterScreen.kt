package com.ryinex.accountant.client.shared.presentation.register.presentation

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cafe.adriel.voyager.core.model.rememberScreenModel
import cafe.adriel.voyager.navigator.LocalNavigator
import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.logo_word
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.data.organization.models.Organization
import com.ryinex.accountant.client.shared.data.users.models.User
import com.ryinex.accountant.client.shared.presentation.register.domain.models.SideEffect
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppButton
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppSecondaryButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppBasePage
import com.ryinex.accountant.client.shared.presentation.common.containers.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.containers.AppRow
import com.ryinex.accountant.client.shared.presentation.common.containers.AppStatusBar
import com.ryinex.accountant.client.shared.presentation.common.dialogs.DelayConfirmDialog
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.texts.AppBodyText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledBodyTextHorizontal
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSubSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTextField
import com.ryinex.accountant.client.shared.presentation.login.presentation.LoginScreen
import com.ryinex.accountant.client.shared.presentation.register.domain.ViewModel
import com.ryinex.accountant.client.shared.presentation.register.domain.models.DataScreenState
import com.ryinex.accountant.client.shared.presentation.register.domain.models.ScreenStateHolder
import com.ryinex.accountant.client.shared.presentation.register.domain.models.toRequest
import com.ryinex.accountant.client.shared.presentation.theme.LocalIsDesktop
import com.ryinex.accountant.client.shared.presentation.theme.TajawalFontFamily
import com.ryinex.accountant.client.shared.presentation.utils.BrowserScreen
import com.ryinex.accountant.client.shared.presentation.utils.browserPop
import com.ryinex.accountant.client.shared.presentation.utils.browserPush
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import kotlin.time.Duration.Companion.seconds

class RegisterScreen : BrowserScreen {

    override val urlSegment: String = "login"

    @Composable
    override fun Content() {
        val navigator = LocalNavigator.current
        val viewModel = rememberScreenModel {
            ViewModel(
                status = TempDI.status,
                auth = TempDI.auth,
                organization = TempDI.organizations,
                logger = TempDI.logger
            )
        }
        val dataState = viewModel.screenState.stream.collectAsState(DataScreenState.initial)
        val state = remember { ScreenStateHolder(data = dataState) }
        val pages = remember(state.ui.organization.isOrganizationFormValid.listen) {
            if (state.ui.organization.isOrganizationFormValid.get()) 3 else 2
        }
        val pager = rememberPagerState { pages }

        var showConfirmDialog by remember { mutableStateOf(false) }
        var user by remember { mutableStateOf<User?>(null) }
        var organization by remember { mutableStateOf<Organization?>(null) }

        LaunchedEffect(Unit) {
            viewModel.sideEffects.stream.collect {
                when (it) {
                    is SideEffect.LoginSuccess -> {
                        showConfirmDialog = true
                        user = it.user
                        organization = it.organization
                    }
                }
            }
        }

        AppBasePage(
            title = "مؤسسة جديدة",
            isBackEnabled = pager.currentPage == 0,
            isPullToRefreshEnabled = false,
            isPullToRefresh = false,
            onBack = { navigator?.browserPop() },
            onPullToRefresh = { },
            screenPadding = PaddingValues(Dimensions.Paddings.screen.dp)
        ) {
            val scope = rememberCoroutineScope()

            ScreenPager(state = state, pager = pager, viewModel = viewModel, pages = pages)

            AppStatusBar(
                statuses = state.data.value.status,
                onCancel = { scope.launch { TempDI.removeStatus(it) } }
            )
        }

        if (showConfirmDialog) {
            ConfirmRegisterDialog(
                user = user!!,
                organization = organization!!,
                onConfirm = {
                    showConfirmDialog = false
                    navigator?.browserPush(LoginScreen(user, organization))
                }
            )
        }
    }
}

@Composable
private fun ScreenPager(
    viewModel: ViewModel,
    state: ScreenStateHolder,
    pager: PagerState,
    pages: Int
) {

    val scope = rememberCoroutineScope()

    HorizontalPager(
        state = pager,
        pageSpacing = Dimensions.Paddings.medium.dp,
        contentPadding = PaddingValues(bottom = Dimensions.Paddings.medium.dp)
    ) {
        when (it) {
            0 -> PagerScreen(
                count = pages,
                current = it,
                bottom = {
                    AppButton(
                        modifier = if (LocalIsDesktop.current) Modifier.fillMaxWidth(0.25f) else Modifier.fillMaxWidth(),
                        text = "التالي",
                        enabled = true
                    ) { scope.launch { pager.animateScrollToPage(1) } }
                },
                content = {
                    DisplayPage(
                        modifier = if (LocalIsDesktop.current) Modifier.fillMaxWidth(0.25f) else Modifier.fillMaxWidth(),
                    )
                }
            )

            1 -> PagerScreen(
                count = pages,
                current = it,
                bottom = {
                    AppRow(
                        modifier = if (LocalIsDesktop.current) Modifier.fillMaxWidth(0.25f) else Modifier.fillMaxWidth(),
                        arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
                        alignment = Alignment.CenterVertically
                    ) {
                        AppSecondaryButton(
                            modifier = Modifier.fillMaxWidth().weight(1f),
                            text = "السابق",
                            enabled = true
                        ) { scope.launch { pager.animateScrollToPage(0) } }
                        AppButton(
                            modifier = Modifier.fillMaxWidth().weight(1f),
                            text = "التالي",
                            enabled = state.ui.organization.isOrganizationFormValid.listen
                        ) { scope.launch { pager.animateScrollToPage(2) } }
                    }
                },
                content = {
                    OrganizationForm(
                        modifier = Modifier.fillMaxHeight().fillMaxWidth(if (LocalIsDesktop.current) 1f else 1f)
                            .verticalScroll(rememberScrollState()),
                        state = state
                    )
                }
            )

            2 -> PagerScreen(
                count = pages,
                current = it,
                bottom = {
                    AppRow(
                        modifier = if (LocalIsDesktop.current) Modifier.fillMaxWidth(0.25f) else Modifier.fillMaxWidth(),
                        arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
                        alignment = Alignment.CenterVertically
                    ) {
                        AppSecondaryButton(
                            modifier = Modifier.fillMaxWidth().weight(1f),
                            text = "السابق",
                            enabled = true,
                        ) { scope.launch { pager.animateScrollToPage(1) } }
                        AppButton(
                            modifier = Modifier.fillMaxWidth().weight(1f),
                            text = "تسجيل",
                            enabled = state.ui.user.isUserFormValid.listen
                        ) { viewModel.register(state.toRequest()) }
                    }
                },
                content = {
                    UserForm(
                        modifier = Modifier.fillMaxHeight().fillMaxWidth(if (LocalIsDesktop.current) 1f else 1f)
                            .verticalScroll(rememberScrollState()),
                        state = state
                    )
                }
            )
        }
    }
}

@Composable
private fun PagerScreen(
    count: Int,
    current: Int,
    bottom: @Composable ColumnScope.() -> Unit,
    content: @Composable BoxScope.() -> Unit
) {
    AppColumn(
        modifier = Modifier.fillMaxSize(),
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
        alignment = Alignment.CenterHorizontally
    ) {
        Box(modifier = Modifier.weight(1f)) { content() }

        AppColumn(
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp),
            alignment = Alignment.CenterHorizontally
        ) {
            Dots(count = count, selectedIndex = current)

            bottom()
        }
    }
}

@Composable
private fun OrganizationForm(
    modifier: Modifier = Modifier,
    state: ScreenStateHolder
) {
    AppColumn(
        modifier = modifier,
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.large.dp),
        alignment = if (LocalIsDesktop.current) Alignment.CenterHorizontally else Alignment.Start
    ) {
        AppSubSectionTitleText("بيانات المؤسسة")

        AppTextField(
            modifier = if (LocalIsDesktop.current) Modifier.fillMaxWidth(0.25f) else Modifier.fillMaxWidth(),
            state = state.ui.organization.name,
            hint = "اسم المؤسسة",
            helperText = "إسم المؤسسة هو الإسم الذي سيظهر في التطبيق بالكامل، " +
                    "مثال: \"مكتب البناء للمقاولات\"، \"شركة الفتح للتوريدات\"، \"محمود لمقاولات الحفر\"",
            errorText = state.ui.organization.nameErrorText,
            enabled = true,
            isError = state.ui.organization.isNameError.listen
        )

        AppTextField(
            modifier = if (LocalIsDesktop.current) Modifier.fillMaxWidth(0.25f) else Modifier.fillMaxWidth(),
            state = state.ui.organization.phoneNumber,
            hint = "رقم الهاتف",
            helperText = "قد يتم إستخدام رقم الهاتف لتفعيل مؤسستك او استرجاع البيانات إذا تم فقدانها",
            errorText = state.ui.organization.phoneNumberErrorText,
            enabled = true,
            isError = state.ui.organization.isPhoneNumberError.listen
        )

        AppTextField(
            modifier = if (LocalIsDesktop.current) Modifier.fillMaxWidth(0.25f) else Modifier.fillMaxWidth(),
            state = state.ui.organization.description,
            hint = "وصف المؤسسة",
            helperText = "وصف مختصر عن المؤسسة او الأعمال التي تقوم بها",
            errorText = state.ui.organization.descriptionErrorText,
            enabled = true,
            isError = state.ui.organization.isDescriptionError.listen
        )
    }
}

@Composable
private fun UserForm(
    modifier: Modifier = Modifier,
    state: ScreenStateHolder
) {
    AppColumn(
        modifier = modifier,
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.large.dp),
        alignment = if (LocalIsDesktop.current) Alignment.CenterHorizontally else Alignment.Start
    ) {
        AppSubSectionTitleText("بيانات المستخدم")
        AppTextField(
            modifier = if (LocalIsDesktop.current) Modifier.fillMaxWidth(0.25f) else Modifier.fillMaxWidth(),
            state = state.ui.user.name,
            hint = "الاسم",
            helperText = "إسمك ثنائي، " +
                    "مثال: \"محمود رزق\"",
            errorText = state.ui.user.nameErrorText,
            enabled = true,
            isError = state.ui.user.isNameError.listen
        )

        AppTextField(
            modifier = if (LocalIsDesktop.current) Modifier.fillMaxWidth(0.25f) else Modifier.fillMaxWidth(),
            state = state.ui.user.username,
            hint = "المستخدم",
            helperText = "إسم المستخدم الذي ستقوم بتسجيل الدخول به الرجاء الاحتفاظ به و عدم فقدانه، مثال: \"mahmoud.rizk\"",
            errorText = state.ui.user.usernameErrorText,
            enabled = true,
            isError = state.ui.user.isUsernameError.listen
        )

        AppTextField(
            modifier = if (LocalIsDesktop.current) Modifier.fillMaxWidth(0.25f) else Modifier.fillMaxWidth(),
            state = state.ui.user.phoneNumber,
            hint = "رقم الهاتف",
            helperText = "قد يتم إستخدام رقم الهاتف لتفعيل مؤسستك او استرجاع البيانات إذا تم فقدانها",
            errorText = state.ui.user.phoneNumberErrorText,
            enabled = true,
            isError = state.ui.user.isPhoneNumberError.listen
        )

        AppTextField(
            modifier = if (LocalIsDesktop.current) Modifier.fillMaxWidth(0.25f) else Modifier.fillMaxWidth(),
            state = state.ui.user.password,
            hint = "كلمة المرور",
            helperText = "كلمة المرور المستخدمة لتسجيل الدخول",
            errorText = state.ui.user.passwordErrorText,
            enabled = true,
            isError = state.ui.user.isPasswordError.listen
        )

        AppTextField(
            modifier = if (LocalIsDesktop.current) Modifier.fillMaxWidth(0.25f) else Modifier.fillMaxWidth(),
            state = state.ui.user.confirmPassword,
            hint = "تأكيد كلمة المرور",
            helperText = "تأكيد كلمة المرور",
            errorText = state.ui.user.confirmPasswordErrorText,
            enabled = true,
            isError = state.ui.user.isConfirmPasswordError.listen
        )
    }
}

@Composable
private fun DisplayPage(
    modifier: Modifier = Modifier
) {
    AppColumn(
        modifier = modifier.fillMaxHeight(),
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.large.dp, Alignment.CenterVertically),
        alignment = if (LocalIsDesktop.current) Alignment.CenterHorizontally else Alignment.CenterHorizontally
    ) {
        val primaryColor = MaterialTheme.colorScheme.primary
        val tertiaryColor = MaterialTheme.colorScheme.tertiaryContainer

        val text = remember {
            buildAnnotatedString {
                withStyle(SpanStyle(fontWeight = FontWeight.Medium, color = primaryColor)) { append("تسجيل\n") }
                withStyle(SpanStyle(fontWeight = FontWeight.Bold, color = tertiaryColor)) { append("مؤسسة ") }
                withStyle(SpanStyle(fontWeight = FontWeight.Medium)) { append("جديدة\n") }
                withStyle(SpanStyle(fontWeight = FontWeight.Light)) { append("بخطوات ") }
                withStyle(SpanStyle(fontWeight = FontWeight.Bold, color = tertiaryColor)) { append("بسيطة") }
            }
        }

        Image(
            modifier = Modifier.width(256.dp),
            painter = painterResource(Res.drawable.logo_word),
            contentDescription = "Settings",
        )

        Text(modifier = Modifier.fillMaxWidth(), text = text, lineHeight = 48.sp, fontSize = 48.sp, fontFamily = TajawalFontFamily)

        AppBodyText(modifier = Modifier.fillMaxWidth(), text = "ابدأ الآن بتسجيل مؤسستك واستمتع بتجربة استخدام تطبيق ذكي يُسهّل إدارة حساباتك، ويجمعك مع شركائك وموظفيك في منصة واحدة لتنظيم الأعمال والمشاريع بكل احترافية، مع إمكانية مشاركة الحسابات بسلاسة مع عملائك والمستفيدين من تجار و وكلاء و مقاوليين.")
    }
}

@Composable
private fun ConfirmRegisterDialog(user: User, organization: Organization, onConfirm: () -> Unit) {
    val title = remember { "مبروووك " }

    DelayConfirmDialog(
        title = title,
        body = { ConfirmRegisterDialogContent(user, organization) },
        onConfirm = { onConfirm() },
        onDismiss = null,
        confirmContainerColor = MaterialTheme.colorScheme.primary,
        confirmTextColor = MaterialTheme.colorScheme.onPrimary,
        delay = 5.seconds
    )
}

@Composable
private fun ConfirmRegisterDialogContent(user: User, organization: Organization) {
    AppColumn(
        modifier = Modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
        alignment = Alignment.Start
    ) {
        AppBodyText(text = "لقد تم التسجيل بنجاح")
        AppLabeledBodyTextHorizontal(label = "اسم المستخدم", text = user.username)
        AppLabeledBodyTextHorizontal(label = "كود المؤسسة", text = organization.organizationCode)
        AppBodyText(text = "الرجاء أخذ لقطة للشاشة (Screenshot) او تسجيلها والأحتفاظ بهذه المعلومات لعدم فقدان الوصول لمؤسستك")
    }
}


@Composable
private fun Dots(modifier: Modifier = Modifier, count: Int, selectedIndex: Int) {
    AppRow(
        modifier = modifier,
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
        alignment = Alignment.CenterVertically
    ) {
        repeat(count) { current ->
            val color = MaterialTheme.colorScheme.tertiary
            Box(
                modifier = Modifier
                    .padding(2.dp)
                    .clip(CircleShape)
                    .background(if (current == selectedIndex) color else MaterialTheme.colorScheme.onTertiary)
                    .border(1.dp, color, CircleShape)
                    .size(8.dp)
            )
        }
    }
}
