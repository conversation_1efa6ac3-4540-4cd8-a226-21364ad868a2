package com.ryinex.accountant.client.shared.presentation.beneficiaries.presentation.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridItemSpan
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import cafe.adriel.voyager.navigator.LocalNavigator
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.containers.AppRow
import com.ryinex.accountant.client.shared.presentation.common.containers.adaptWidth
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.texts.AppPhoneNumberTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTextField
import com.ryinex.accountant.client.shared.presentation.customers.presentation.ui.CustomerCard
import com.ryinex.accountant.client.shared.presentation.utils.browserPush
import com.ryinex.accountant.client.shared.data.beneficiaries.models.Beneficiary
import com.ryinex.accountant.client.shared.domain.beneficiaries.home.ViewModel
import com.ryinex.accountant.client.shared.domain.beneficiaries.home.models.ScreenState
import com.ryinex.accountant.client.shared.presentation.common.components.SearchBar
import com.ryinex.accountant.client.shared.presentation.common.surface.AppBottomSheet

internal fun LazyStaggeredGridScope.BeneficiariesListSection(
    viewModel: ViewModel,
    state: ScreenState
) {

    item(span = StaggeredGridItemSpan.FullLine) {
        SearchBar(
            modifier = Modifier.adaptWidth(),
            onValueChange = { viewModel.search(it) },
            value = state.search,
            enabled = true
        )
    }

    state.filteredBeneficiaries.forEach { beneficiary ->
        item {
            val navigator = LocalNavigator.current

            CustomerCard(
                modifier = Modifier.adaptWidth(),
                name = beneficiary.name,
                phoneNumber = beneficiary.phoneNumber,
                enabled = state.isBeneficiaryDetailsEnabled,
                onClick = { navigator?.browserPush(BeneficiaryTransactionsScreen(beneficiary)) }
            )
        }
    }
}