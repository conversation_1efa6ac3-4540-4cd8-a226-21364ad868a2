package com.ryinex.accountant.client.shared.presentation.terms

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.data.terms.models.Term
import com.ryinex.accountant.client.shared.data.terms.models.TermsGroup
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.containers.AppRow
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.radiobutton.AppLabeledRadioButton
import com.ryinex.accountant.client.shared.presentation.common.state.BooleanState
import com.ryinex.accountant.client.shared.presentation.common.state.DerivedState
import com.ryinex.accountant.client.shared.presentation.common.state.IntState
import com.ryinex.accountant.client.shared.presentation.common.state.MutableItemState
import com.ryinex.accountant.client.shared.presentation.common.state.StringState
import com.ryinex.accountant.client.shared.presentation.common.surface.AppBottomSheet
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSelectableAutoCompleteTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTextField
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.execute
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch

private class StateHolder(
    val name: StringState = StringState(""),
    val description: StringState = StringState(""),
    val termsGroup: MutableItemState<TermsGroup?> = MutableItemState(null),
    val termsGroupText: StringState = StringState(""),
    val newTermsGroupName: StringState = StringState(""),
    val newTermsGroupDescription: StringState = StringState(""),
    val selectedRadioButton: IntState = IntState(-1),
    val isLoading: BooleanState = BooleanState(false)
) {
    private val minNameLength = 3
    private val minDescriptionLength = 3

    val isNameError = DerivedState(true, name.stream.map { it.trim().length < minNameLength })
    val isDescriptionError = DerivedState(true, description.stream.map { it.trim().length < minDescriptionLength })
    val isNewTermsGroupNameError = DerivedState(true, newTermsGroupName.stream.map { it.trim().length < minNameLength })
    val isNewTermsGroupDescriptionError =
        DerivedState(true, newTermsGroupDescription.stream.map { it.trim().length < minDescriptionLength })

    private val actionEnabledStream = com.ryinex.accountant.client.shared.data.common.utilities.combine(
        isNameError.stream,
        isDescriptionError.stream,
        isNewTermsGroupNameError.stream,
        isNewTermsGroupDescriptionError.stream,
        selectedRadioButton.stream,
        termsGroup.stream,
        isLoading.stream
    ) { nameError, descriptionError, newGroupNameError, newGroupDescError, radioButton, group, loading ->
        val isGroupError = when (radioButton) {
            0 -> newGroupNameError || newGroupDescError
            1 -> group == null
            else -> true
        }
        !loading && !nameError && !descriptionError && !isGroupError
    }

    val actionEnabled = DerivedState(false, actionEnabledStream)

    fun fromTerm(term: Term) {
        name.update(term.name)
        description.update(term.description)
        termsGroup.update(term.termsGroup)
        termsGroupText.update(term.termsGroup.name)
        selectedRadioButton.update(1) // Select existing group option
    }

    fun setInitialTermsGroup(group: TermsGroup?) {
        if (group != null) {
            termsGroup.update(group)
            termsGroupText.update(group.name)
            selectedRadioButton.update(1)
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TermSheet(
    term: Term? = null,
    initialTermsGroup: TermsGroup? = null,
    groups: List<TermsGroup> = emptyList(),
    isCancelTermsGroupEnabled: Boolean = true,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    val state = remember { StateHolder() }

    LaunchedEffect(Unit) {
        if (term != null) {
            state.fromTerm(term)
        } else if (initialTermsGroup != null) {
            state.setInitialTermsGroup(initialTermsGroup)
        }
    }

    AppBottomSheet(onDismissRequest = onDismissRequest) {
        AppLazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            SheetContent(
                state = state,
                term = term,
                groups = groups,
                isCancelTermsGroupEnabled = isCancelTermsGroupEnabled,
                onDismissRequest = onDismissRequest,
                onFinish = onFinish
            )
        }
    }
}

private fun LazyListScope.SheetContent(
    state: StateHolder,
    term: Term?,
    groups: List<TermsGroup>,
    isCancelTermsGroupEnabled: Boolean,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    item {
        val title = if (term != null) "تعديل بند أعمال" else "إضافة بند أعمال"
        AppSectionTitleText(title)
    }

    item { ActionsRow(state = state, term = term, onDismissRequest = onDismissRequest, onFinish = onFinish) }

    item { NameInput(state = state) }

    item { DescriptionInput(state = state) }

    if (state.termsGroup.value == null) {
        item { CreateNewGroupRadioButton(state = state) }
        item { SelectExistingGroupRadioButton(state = state) }
    }

    if (state.selectedRadioButton.value == 0) {
        item { NewTermsGroupNameInput(state = state) }
        item { NewTermsGroupDescriptionInput(state = state) }
    }

    if (state.selectedRadioButton.value == 1 || state.termsGroup.value != null) {
        item {
            TermsGroupSelector(
                state = state,
                groups = groups,
                isCancelTermsGroupEnabled = isCancelTermsGroupEnabled
            )
        }
    }
}

@Composable
private fun NameInput(state: StateHolder) {
    AppTextField(
        modifier = Modifier.fillMaxWidth(),
        text = state.name.listen,
        hint = "الاسم",
        enabled = !state.isLoading.listen,
        isError = state.isNameError.listen,
        onValueChange = { state.name.update(it) },
        errorText = "البند يجب أن يكون على الأقل 3 أحرف"
    )
}

@Composable
private fun DescriptionInput(state: StateHolder) {
    AppTextField(
        modifier = Modifier.fillMaxWidth(),
        text = state.description.listen,
        hint = "الوصف",
        enabled = !state.isLoading.listen,
        isError = state.isDescriptionError.listen,
        onValueChange = { state.description.update(it) },
        errorText = "الوصف يجب أن يكون على الأقل 3 أحرف"
    )
}

@Composable
private fun CreateNewGroupRadioButton(state: StateHolder) {
    AppLabeledRadioButton(
        modifier = Modifier.fillMaxWidth(),
        enabled = !state.isLoading.listen,
        label = "إنشاء مجموعة أعمال جديدة",
        selected = state.selectedRadioButton.value == 0,
        onClick = { state.selectedRadioButton.update(0) }
    )
}

@Composable
private fun SelectExistingGroupRadioButton(state: StateHolder) {
    AppLabeledRadioButton(
        modifier = Modifier.fillMaxWidth(),
        enabled = !state.isLoading.listen,
        label = "إختيار مجموعة أعمال",
        selected = state.selectedRadioButton.value == 1,
        onClick = { state.selectedRadioButton.update(1) }
    )
}

@Composable
private fun NewTermsGroupNameInput(state: StateHolder) {
    AppTextField(
        modifier = Modifier.fillMaxWidth(),
        text = state.newTermsGroupName.value,
        hint = "الإسم",
        enabled = !state.isLoading.listen,
        isError = state.isNewTermsGroupNameError.value,
        errorText = "المجموعة يجب أن يكون على الأقل 3 أحرف",
        onValueChange = { state.newTermsGroupName.update(it) }
    )
}

@Composable
private fun NewTermsGroupDescriptionInput(state: StateHolder) {
    AppTextField(
        modifier = Modifier.fillMaxWidth(),
        text = state.newTermsGroupDescription.value,
        hint = "الوصف",
        enabled = !state.isLoading.listen,
        isError = state.isNewTermsGroupDescriptionError.value,
        errorText = "الوصف يجب أن يكون على الأقل 3 أحرف",
        onValueChange = { state.newTermsGroupDescription.update(it) }
    )
}

@Composable
private fun TermsGroupSelector(
    state: StateHolder,
    groups: List<TermsGroup>,
    isCancelTermsGroupEnabled: Boolean
) {
    AppSelectableAutoCompleteTextField(
        modifier = Modifier.fillMaxWidth(),
        items = groups,
        text = state.termsGroupText.value,
        onValueChange = { state.termsGroupText.update(it) },
        hint = "مجموعة الأعمال",
        enabled = !state.isLoading.listen,
        isError = false,
        errorText = "",
        selected = state.termsGroup.value,
        onItemClick = { state.termsGroup.update(it) },
        displayMapper = { it.name },
        searchMapper = { it.name },
        onCancelSelected = { state.termsGroup.update(null) },
        isCancelSelectedEnabled = isCancelTermsGroupEnabled,
        onEmptyItemClick = null
    )
}

@Composable
private fun ActionsRow(
    modifier: Modifier = Modifier,
    state: StateHolder,
    term: Term?,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    val scope = rememberCoroutineScope()

    AppRow(
        modifier = modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
        alignment = Alignment.CenterVertically
    ) {
        AppTextButton(
            modifier = Modifier,
            text = "إلغاء",
            enabled = !state.isLoading.listen,
            onClick = onDismissRequest
        )

        AppTextButton(
            modifier = Modifier,
            text = if (term != null) "تعديل" else "إضافة",
            enabled = state.actionEnabled.listen,
            onClick = {
                scope.launch {
                    if (term != null) {
                        updateTerm(state = state, term = term, onFinish = onFinish)
                    } else {
                        addTerm(state = state, onFinish = onFinish)
                    }
                    onDismissRequest()
                }
            }
        )
    }
}

private suspend fun addTerm(
    state: StateHolder,
    onFinish: () -> Unit
) {
    jobWrapper(
        state = state,
        loading = "جاري إضافة البند ...",
        success = "تم إضافة البند",
        job = { addTermJob(state = state, onFinish = onFinish) }
    )
}

private suspend fun addTermJob(
    state: StateHolder,
    onFinish: () -> Unit
) {
    // Create new terms group if needed
    var termsGroup = state.termsGroup.get()
    if (state.selectedRadioButton.get() == 0) {
        termsGroup = TempDI.termsGroups.create(
            name = state.newTermsGroupName.get(),
            description = state.newTermsGroupDescription.get()
        )
        delay(1000) // Wait for group creation
    }

    // Create the term
    TempDI.terms.create(
        groupId = termsGroup!!.id,
        name = state.name.get(),
        description = state.description.get()
    )

    onFinish()
}

private suspend fun updateTerm(
    state: StateHolder,
    term: Term,
    onFinish: () -> Unit
) {
    jobWrapper(
        state = state,
        loading = "جاري تعديل البند ...",
        success = "تم تعديل البند",
        job = { updateTermJob(term = term, state = state, onFinish = onFinish) }
    )
}

private suspend fun updateTermJob(
    term: Term,
    state: StateHolder,
    onFinish: () -> Unit
) {
    TempDI.terms.edit(
        termId = term.id,
        name = state.name.get(),
        description = state.description.get(),
        version = term.version
    )

    onFinish()
}

private suspend fun <T> jobWrapper(state: StateHolder, loading: String, success: String, job: suspend () -> T): T? {
    val status = TempDI.status
    val errorHolder = TempDI.errorHandler

    return errorHolder.execute {
        state.isLoading.update(true)

        val result = status.execute(
            loading = Message.fromString(loading),
            success = Message.fromString(success),
            job = job
        )

        state.isLoading.update(false)

        return@execute if (result is AsyncResult.Fail) throw result.error else result.result
    }
}

// Backward compatibility function
@Deprecated(
    "Use TermSheet instead",
    ReplaceWith("TermSheet(term = null, initialTermsGroup = termsGroup, groups = groups, isCancelTermsGroupEnabled = isCancelTermsGroupEnabled, onDismissRequest = onDismissRequest, onFinish = onFinish)")
)
@Composable
fun AddTermSheet(
    termsGroup: TermsGroup?,
    groups: List<TermsGroup>,
    isCancelTermsGroupEnabled: Boolean,
    onFinish: () -> Unit,
    onDismissRequest: () -> Unit
) {
    TermSheet(
        term = null,
        initialTermsGroup = termsGroup,
        groups = groups,
        isCancelTermsGroupEnabled = isCancelTermsGroupEnabled,
        onDismissRequest = onDismissRequest,
        onFinish = onFinish
    )
}
