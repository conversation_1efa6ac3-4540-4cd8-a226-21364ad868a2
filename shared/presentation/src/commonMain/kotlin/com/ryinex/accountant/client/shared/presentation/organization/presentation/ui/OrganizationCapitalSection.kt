package com.ryinex.accountant.client.shared.presentation.organization.presentation.ui

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppCard
import com.ryinex.accountant.client.shared.presentation.common.containers.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.containers.AppFlowRow
import com.ryinex.accountant.client.shared.presentation.common.containers.AppRow
import com.ryinex.accountant.client.shared.presentation.common.containers.ForceLtr
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.images.AppSupportImageViewButton
import com.ryinex.accountant.client.shared.presentation.common.modifiers.sectionsSpacing
import com.ryinex.accountant.client.shared.presentation.common.texts.AppDecimalTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabelText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledLabelTextHorizontal
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTitleText
import com.ryinex.accountant.client.shared.domain.organization.ViewModel
import com.ryinex.accountant.client.shared.domain.organization.models.ScreenState
import com.ryinex.accountant.client.shared.data.common.utilities.AppDouble
import com.ryinex.accountant.client.shared.data.common.utilities.app
import com.ryinex.accountant.client.shared.data.common.utilities.numberFormat
import com.ryinex.accountant.client.shared.data.organization.models.OrganizationCapitalTransaction
import com.ryinex.accountant.client.shared.presentation.common.shadowBluish
import com.ryinex.accountant.client.shared.presentation.common.surface.AppBottomSheet
import com.ryinex.kotlin.datatable.data.DataTable
import com.ryinex.kotlin.datatable.views.EmbeddedDataTableView
import core.common.extensions.dateFormat
import kotlinx.datetime.Clock

internal fun LazyListScope.OrganizationCapitalSection(
    state: ScreenState,
    viewModel: ViewModel,
    isDesktop: Boolean,
    scrollableState: ScrollState,
    table: DataTable<OrganizationCapitalTransaction>
) {
    item {
        OrganizationCapitalEdit(state, viewModel)
    }

    if (isDesktop) {
        EmbeddedDataTableView(scrollableState, table)
    } else {
        for (item in state.transactions) {
            item {
                TransactionCard(
                    amount = item.amount,
                    description = item.description,
                    createdBy = item.createdByUser.name,
                    createdAtIseoFormat = item.createdAt
                )
            }
        }
    }
}

@Composable
fun TransactionCard(
    amount: AppDouble,
    description: String,
    createdBy: String,
    createdAtIseoFormat: String,
    modifier: Modifier = Modifier
) {
    AppCard(
        modifier = modifier.fillMaxWidth().shadowBluish()
    ) {
        var extended by remember { mutableStateOf(false) }
        AppColumn(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { extended = !extended }
                .padding(Dimensions.Paddings.medium.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
            alignment = Alignment.Start
        ) {
            AppRow(
                arrangement = Arrangement.SpaceBetween,
                alignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier.fillMaxWidth().weight(1f),
                    contentAlignment = if (LayoutDirection.Rtl == LocalLayoutDirection.current) Alignment.CenterStart else Alignment.CenterEnd
                ) {
                    ForceLtr {
                        AppTitleText(
                            text = amount.formatted(),
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }

                AppSupportImageViewButton(
                    painter = rememberVectorPainter(if (extended) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown),
                    enabled = true,
                    onClick = { extended = !extended },
                    backgroundColor = Color.Transparent,
                    tint = MaterialTheme.colorScheme.tertiary
                )
            }

            AppColumn(
                modifier = Modifier,
                arrangement = Arrangement.Top,
                alignment = Alignment.Start
            ) {
                if (description.isNotBlank()) {
                    AppLabelText(
                        text = description,
                        color = LocalContentColor.current
                    )
                } else {
                    AppLabeledLabelTextHorizontal(
                        label = "بواسطة",
                        text = createdBy
                    )
                }

                if (extended) {
                    if (description.isNotBlank()) {
                        AppLabeledLabelTextHorizontal(
                            label = "بواسطة",
                            text = createdBy
                        )
                    }

                    AppLabeledLabelTextHorizontal(
                        label = "بتاريخ",
                        text = createdAtIseoFormat.dateFormat(),
                        ltrText = true
                    )
                }
            }

        }
    }
}

@Composable
private fun OrganizationCapitalEdit(
    state: ScreenState,
    viewModel: ViewModel
) {
    var showAddSheet by remember { mutableStateOf(false) }
    var showMoveSheet by remember { mutableStateOf(false) }

    AppFlowRow(
        modifier = Modifier.fillMaxWidth().sectionsSpacing(),
        arrangement = Arrangement.SpaceBetween,
        alignment = Arrangement.Start
    ) {
        AppSectionTitleText("حركة رأس المال", modifier = Modifier.weight(1f))

        AppRow(
            modifier = Modifier,
            arrangement = Arrangement.Start,
            alignment = Alignment.Top
        ) {
            AppTextButton(
                modifier = Modifier,
                text = "سحب",
                enabled = state.isRemoveCapitalEnabled,
                onClick = { showMoveSheet = true }
            )

            AppTextButton(
                modifier = Modifier,
                text = "إضافة",
                enabled = state.isAddCapitalEnabled,
                onClick = { showAddSheet = true }
            )
        }
    }

    if (showAddSheet) {
        AddBottomSheet(
            onDismissRequest = {
                showAddSheet = false
            },
            onAddCapital = { amount, description ->
                viewModel.addCapital(amount, description, Clock.System.now())
            },
        )
    }

    if (showMoveSheet) {
        OrganizationCapitalMove(
            onDismissRequest = {
                showMoveSheet = false
            },
            onMoveCapital = { amount, description ->
                viewModel.removeCapital(amount, description, Clock.System.now())
            },
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AddBottomSheet(
    onAddCapital: (AppDouble, String) -> Unit,
    onDismissRequest: () -> Unit
) {
    AppBottomSheet(onDismissRequest = onDismissRequest) {
        var description by remember { mutableStateOf("") }
        var amount by remember { mutableStateOf(0.0.app()) }

        Column(
            modifier = Modifier.fillMaxWidth().padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                modifier = Modifier.padding(bottom = 16.dp),
                text = "إضافة رأس مال",
                style = MaterialTheme.typography.titleLarge
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp, alignment = Alignment.End)
            ) {
                TextButton(
                    modifier = Modifier,
                    onClick = {
                        onDismissRequest()
                    },
                ) {
                    Text("إلغاء")
                }

                TextButton(
                    modifier = Modifier,
                    enabled = amount > 0.0 && description.trim().length >= 4,
                    onClick = {
                        onAddCapital(amount, description)
                        onDismissRequest()
                    },
                ) {
                    Text("إضافة")
                }
            }

            AppDecimalTextField(
                modifier = Modifier.fillMaxWidth(),
                amount = amount,
                hint = "القيمة",
                enabled = true,
                isError = amount <= 0.0,
                onlyPositive = true,
                onValueChange = { amount = it },
            )

            AppTextField(
                modifier = Modifier.fillMaxWidth(),
                text = description,
                hint = "الوصف",
                enabled = true,
                isError = description.trim().length < 4,
                onValueChange = { description = it },
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun OrganizationCapitalMove(
    onMoveCapital: (AppDouble, String) -> Unit,
    onDismissRequest: () -> Unit
) {
    AppBottomSheet(
        onDismissRequest = onDismissRequest
    ) {
        var description by remember { mutableStateOf("") }
        var amount by remember { mutableStateOf(0.0.app()) }

        Column(
            modifier = Modifier.fillMaxWidth().padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                modifier = Modifier.padding(bottom = 16.dp),
                text = "سحب من رأس المال",
                style = MaterialTheme.typography.titleLarge
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp, alignment = Alignment.End)
            ) {
                TextButton(
                    modifier = Modifier,
                    onClick = {
                        onDismissRequest()
                    },
                ) {
                    Text("إلغاء")
                }

                TextButton(
                    modifier = Modifier,
                    enabled = amount > 0.0 && description.trim().length >= 4,
                    onClick = {
                        onMoveCapital(amount, description)
                        onDismissRequest()
                    },
                ) {
                    Text("سحب")
                }
            }

            AppDecimalTextField(
                modifier = Modifier.fillMaxWidth(),
                amount = amount,
                hint = "القيمة",
                enabled = true,
                isError = amount <= 0.0,
                onlyPositive = true,
                onValueChange = { amount = it },
            )

            AppTextField(
                modifier = Modifier.fillMaxWidth(),
                text = description,
                hint = "الوصف",
                enabled = true,
                isError = description.trim().length < 4,
                onValueChange = { description = it },
            )
        }
    }
}