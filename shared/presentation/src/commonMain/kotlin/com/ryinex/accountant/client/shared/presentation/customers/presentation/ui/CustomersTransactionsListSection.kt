package com.ryinex.accountant.client.shared.presentation.customers.presentation.ui

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.ModalBottomSheetDefaults
import androidx.compose.material3.SheetValue
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppCard
import com.ryinex.accountant.client.shared.presentation.common.containers.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.containers.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.containers.AppRow
import com.ryinex.accountant.client.shared.presentation.common.containers.ForceLtr
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.images.AppSupportImageViewButton
import com.ryinex.accountant.client.shared.presentation.common.texts.AppDecimalTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabelText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledLabelTextHorizontal
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTitleText
import com.ryinex.accountant.client.shared.data.customers.repositories.CustomersTransactionsRepository
import com.ryinex.accountant.client.shared.data.projects.models.ProjectIncome
import com.ryinex.accountant.client.shared.data.projects.repositories.ProjectsRepository
import com.ryinex.accountant.client.shared.data.common.error.ErrorHandler
import com.ryinex.accountant.client.shared.data.common.utilities.AppDouble
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.data.common.utilities.app
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppDelayedConfirmButton
import com.ryinex.accountant.client.shared.presentation.common.shadowBluish
import com.ryinex.accountant.client.shared.presentation.common.surface.AppBottomSheet
import com.ryinex.kotlin.datatable.data.DataTable
import com.ryinex.kotlin.datatable.views.EmbeddedDataTableView
import core.common.extensions.dateFormat
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import kotlinx.coroutines.launch

internal fun LazyListScope.CustomersTransactionsListSection(
    table: DataTable<ProjectIncome>,
    scrollState: ScrollState,
    isEditEnabled: Boolean,
    transactions: List<ProjectIncome>,
    isPaymentEnabled: Boolean,
    isDesktop: Boolean,
    onFinishPayment: () -> Unit,
    onRefresh: () -> Unit,
) {
    if (isDesktop) {
        EmbeddedDataTableView(scrollState, table)
    } else {
        transactions.forEach { item ->
            item {
                var showPaymentSheet by remember { mutableStateOf(false) }
                var showEditSheet by remember { mutableStateOf(false) }
                var income by remember { mutableStateOf<ProjectIncome?>(null) }

                CustomerTransactionCard(
                    income = item,
                    projectName = item.project.name,
                    isPaymentEnabled = isPaymentEnabled && item.customerTransaction.amountPaid < item.customerTransaction.amount,
                    isEditEnabled = isEditEnabled,
                    onFinishEdit = onRefresh,
                    onPaymentClick = {
                        income = item
                        showPaymentSheet = true
                    }
                )

                if (showPaymentSheet && income != null) {
                    PaymentSheet(
                        customerId = income!!.customerTransaction.customerId,
                        transactionId = income!!.customerTransaction.id,
                        version = income!!.customerTransaction.version,
                        initialAmount = income!!.customerTransaction.amountPaid,
                        onDismissRequest = {
                            income = null
                            showPaymentSheet = false
                        },
                        onFinish = onFinishPayment
                    )
                }

                if (showEditSheet && income != null) {
                    EditTransactionSheet(
                        income = income!!,
                        onDismissRequest = {
                            income = null
                            showEditSheet = false
                        },
                        onFinish = onFinishPayment
                    )
                }
            }
        }
    }
}


@Composable
private fun CustomerTransactionCard(
    income: ProjectIncome,
    isEditEnabled: Boolean,
    isPaymentEnabled: Boolean,
    projectName: String,
    onPaymentClick: () -> Unit,
    onFinishEdit: () -> Unit,
    modifier: Modifier = Modifier
) {
    AppCard(
        modifier = modifier.fillMaxWidth().shadowBluish()
    ) {
        var extended by remember { mutableStateOf(false) }

        AppColumn(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { extended = !extended }
                .padding(Dimensions.Paddings.medium.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
            alignment = Alignment.Start
        ) {

            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.SpaceBetween,
                alignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier.fillMaxWidth().weight(1f),
                    contentAlignment = if (LayoutDirection.Rtl == LocalLayoutDirection.current) Alignment.CenterStart else Alignment.CenterEnd
                ) {
                    ForceLtr {
                        AppTitleText(
                            text = income.customerTransaction.amount.formatted(),
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }

                AppColumn(
                    arrangement = Arrangement.Top,
                    alignment = Alignment.End
                ) {
                    var showEditSheet by remember { mutableStateOf(false) }

                    if (isPaymentEnabled && income.customerTransaction.amountPaid < income.customerTransaction.amount) {
                        AppTextButton(
                            modifier = Modifier,
                            text = "تحصيل",
                            enabled = isPaymentEnabled,
                            onClick = onPaymentClick
                        )
                    }

                    AppRow(
                        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                        alignment = Alignment.CenterVertically
                    ) {
                        if (isEditEnabled) {
                            AppSupportImageViewButton(
                                painter = rememberVectorPainter(Icons.Default.Edit),
                                enabled = isEditEnabled,
                                onClick = { showEditSheet = true },
                            )
                        }

                        AppSupportImageViewButton(
                            painter = rememberVectorPainter(if (extended) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown),
                            enabled = true,
                            onClick = { extended = !extended },
                            backgroundColor = Color.Transparent,
                            tint = MaterialTheme.colorScheme.tertiary
                        )
                    }

                    if (showEditSheet) {
                        EditTransactionSheet(
                            income = income,
                            onDismissRequest = { showEditSheet = false },
                            onFinish = {
                                showEditSheet = false
                                onFinishEdit()
                            }
                        )
                    }
                }
            }


            AppColumn(
                modifier = Modifier,
                arrangement = Arrangement.Top,
                alignment = Alignment.Start
            ) {
                if (income.customerTransaction.description.isNotBlank()) {
                    AppLabelText(
                        text = income.customerTransaction.description,
                        color = LocalContentColor.current
                    )
                } else {
                    AppLabeledLabelTextHorizontal(
                        label = "بواسطة",
                        text = income.customerTransaction.createdByUser.name
                    )
                }

                if (income.customerTransaction.amountPaid < income.customerTransaction.amount) {
                    AppLabeledLabelTextHorizontal(
                        label = "تم تحصيل",
                        text = income.customerTransaction.amountPaid.formatted()
                    )
                }

                if (income.customerTransaction.remaining > 0.0.app()) {
                    AppLabeledLabelTextHorizontal(
                        label = "المتبقى",
                        text = income.customerTransaction.remaining.formatted()
                    )
                }

                if (extended) {
                    AppLabeledLabelTextHorizontal(
                        label = "المشروع",
                        text = projectName
                    )

                    if (income.customerTransaction.description.isNotBlank()) {
                        AppLabeledLabelTextHorizontal(
                            label = "بواسطة",
                            text = income.customerTransaction.createdByUser.name
                        )
                    }

                    AppLabeledLabelTextHorizontal(
                        label = "بتاريخ",
                        text = income.customerTransaction.createdAt.dateFormat(),
                        ltrText = true
                    )
                }
            }

        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun PaymentSheet(
    initialAmount: AppDouble,
    customerId: Long,
    transactionId: Long,
    version: Long,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    val scope = rememberCoroutineScope()
    var isLoading by remember { mutableStateOf(false) }

    AppBottomSheet(onDismissRequest = onDismissRequest) {
        var amount by remember { mutableStateOf(initialAmount) }

        val isEnabled by remember(amount, isLoading) {
            mutableStateOf(amount > initialAmount && !isLoading)
        }

        AppColumn(
            modifier = Modifier.fillMaxWidth().padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            AppSectionTitleText(
                modifier = Modifier,
                text = "الدفع"
            )

            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                alignment = Alignment.CenterVertically
            ) {
                AppTextButton(
                    modifier = Modifier,
                    text = "إلغاء",
                    enabled = true,
                    onClick = { if (!isLoading) onDismissRequest() }
                )

                AppTextButton(
                    modifier = Modifier,
                    text = "دفع",
                    enabled = isEnabled,
                    onClick = {
                        scope.launch {
                            isLoading = true
                            pay(
                                amount = amount,
                                customerId = customerId,
                                transactionId = transactionId,
                                version = version,
                                onFinish = {
                                    onDismissRequest()
                                    onFinish()
                                },
                                onError = {
                                    isLoading = false
                                    onDismissRequest()
                                }
                            )
                            isLoading = false
                            onDismissRequest()
                        }
                    },
                )
            }

            AppDecimalTextField(
                modifier = Modifier.fillMaxWidth(),
                amount = amount,
                hint = "القيمة",
                enabled = true,
                isError = !isEnabled,
                onlyPositive = true,
                onValueChange = { amount = it },
                errorText = "القيمة الجديدة يجب أن أكثر من الحالية"
            )
        }
    }
}

private suspend fun pay(
    amount: AppDouble,
    customerId: Long,
    transactionId: Long,
    version: Long,
    transactions: CustomersTransactionsRepository = TempDI.incomes,
    status: StatusRepository = TempDI.status,
    errorHandler: ErrorHandler = TempDI.errorHandler,
    onFinish: () -> Unit,
    onError: () -> Unit
) {
    errorHandler.execute {
        val result = status.execute(
            loading = Message.fromString("جاري التحصيل ..."),
            success = Message.fromString("تم التحصيل"),
            job = {
                transactions.pay(
                    customerId = customerId,
                    transactionId = transactionId,
                    amount = amount,
                    version = version
                )
                onFinish()
            }
        )

        if (result is AsyncResult.Fail) {
            onError()
            throw result.error
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun EditTransactionSheet(
    income: ProjectIncome,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    val minDescriptionLength = remember { 3 }
    val scope = rememberCoroutineScope()
    var isLoading by remember { mutableStateOf(false) }

    AppBottomSheet(onDismissRequest = onDismissRequest) {
        var description by remember { mutableStateOf(income.customerTransaction.description) }
        var amount by remember { mutableStateOf(income.customerTransaction.amount) }
        var amountPaid by remember { mutableStateOf(income.customerTransaction.amountPaid) }

        val isDescriptionError by remember(description) { mutableStateOf(description.length < minDescriptionLength) }
        val isAmountError by remember(amount) { mutableStateOf(false) }
        val isAmountPaidError by remember(amountPaid, amount) { mutableStateOf(amountPaid > amount) }

        val isChangeAmountEnabled by remember(isAmountError, isAmountPaidError, isLoading) {
            mutableStateOf(!isAmountError && !isLoading && !isAmountPaidError)
        }
        val isDetailsEnabled by remember(isDescriptionError, isLoading) {
            mutableStateOf(!isDescriptionError && !isLoading)
        }

        AppLazyColumn(
            modifier = Modifier.fillMaxHeight().padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            item { AppSectionTitleText(text = "تعديل التحويل") }

            item {
                AppDecimalTextField(
                    modifier = Modifier.fillMaxWidth(),
                    amount = amount,
                    hint = "القيمة",
                    enabled = !isLoading,
                    isError = isAmountError,
                    onlyPositive = true,
                    onValueChange = { amount = it },
                    errorText = ""
                )
            }

            item {
                AppDecimalTextField(
                    modifier = Modifier.fillMaxWidth(),
                    amount = amountPaid,
                    hint = "تم تحصيل",
                    enabled = !isLoading,
                    isError = isAmountPaidError,
                    onlyPositive = true,
                    onValueChange = { amountPaid = it },
                    errorText = "المبلغ المدفوع يجب أن يكون أقل من القيمة"
                )
            }

            item {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                    alignment = Alignment.CenterVertically
                ) {
                    AppTextButton(text = "إلغاء", enabled = true, onClick = { if (!isLoading) onDismissRequest() })

                    AppTextButton(
                        text = "تعديل",
                        enabled = isChangeAmountEnabled,
                        onClick = {
                            scope.launch {
                                isLoading = true
                                changeIncomeAmount(
                                    amount = amount,
                                    amountPaid = amountPaid,
                                    projectIncomeId = income.id,
                                    projectId = income.project.id,
                                    transactionVersion = income.customerTransaction.version,
                                    onError = { isLoading = false }
                                )
                                isLoading = false
                                onFinish()
                                onDismissRequest()
                            }
                        }
                    )
                }
            }

            item { HorizontalDivider() }

            item {
                AppTextField(
                    modifier = Modifier.fillMaxWidth(),
                    text = description,
                    hint = "الوصف",
                    enabled = !isLoading,
                    isError = isDescriptionError,
                    errorText = "الوصف يجب أن يكون على الأقل $minDescriptionLength أحرف",
                    onValueChange = { description = it }
                )
            }

            item {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                    alignment = Alignment.CenterVertically
                ) {
                    AppTextButton(
                        modifier = Modifier,
                        text = "إلغاء",
                        enabled = true,
                        onClick = { onDismissRequest() }
                    )

                    AppTextButton(
                        modifier = Modifier,
                        text = "تعديل",
                        enabled = isDetailsEnabled,
                        onClick = {
                            scope.launch {
                                isLoading = true
                                updateCustomerTransaction(
                                    customerId = income.customer.id,
                                    transactionId = income.customerTransaction.id,
                                    description = description,
                                    version = income.customerTransaction.version,
                                    onError = { isLoading = false }
                                )
                                isLoading = false
                                onFinish()
                                onDismissRequest()
                            }
                        }
                    )
                }
            }

            item { HorizontalDivider() }

            item {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                    alignment = Alignment.CenterVertically
                ) {
                    AppDelayedConfirmButton(
                        modifier = Modifier,
                        text = "حذف التحويل",
                        enabled = !isLoading,
                        containerColor = MaterialTheme.colorScheme.error,
                        onClick = {
                            scope.launch {
                                isLoading = true
                                deleteIncome(
                                    projectIncomeId = income.id,
                                    projectId = income.project.id,
                                    onError = { isLoading = false }
                                )
                                isLoading = false
                                onFinish()
                                onDismissRequest()
                            }
                        }
                    )
                }
            }
        }
    }
}

@Composable
internal fun CustomersTransactionsListSectionBottomSheets(
    payIncome: MutableState<ProjectIncome?>,
    editIncome: MutableState<ProjectIncome?>,
    onFinishPayment: () -> Unit
) {
    if (payIncome.value != null) {
        PaymentSheet(
            customerId = payIncome.value!!.customerTransaction.customerId,
            transactionId = payIncome.value!!.customerTransaction.id,
            version = payIncome.value!!.customerTransaction.version,
            initialAmount = payIncome.value!!.customerTransaction.amountPaid,
            onDismissRequest = { payIncome.value = null },
            onFinish = onFinishPayment
        )
    }

    if (editIncome.value != null) {
        EditTransactionSheet(
            income = editIncome.value!!,
            onDismissRequest = { editIncome.value = null },
            onFinish = onFinishPayment
        )
    }
}

private suspend fun changeIncomeAmount(
    amount: AppDouble,
    amountPaid: AppDouble,
    projectIncomeId: Long,
    projectId: Long,
    transactionVersion: Long,
    status: StatusRepository = TempDI.status,
    errorHandler: ErrorHandler = TempDI.errorHandler,
    projects: ProjectsRepository = TempDI.projects,
    onError: () -> Unit
) {
    errorHandler.execute {
        val result = status.execute(
            loading = Message.fromString("جاري تعديل القيمة ..."),
            success = Message.fromString("تم تعديل القيمة"),
            job = {
                projects.changeIncomeAmount(
                    amount = amount,
                    amountPaid = amountPaid,
                    projectIncomeId = projectIncomeId,
                    projectId = projectId,
                    transactionVersion = transactionVersion
                )
            }
        )

        if (result is AsyncResult.Fail) {
            onError()
            throw result.error
        }
    }
}

private suspend fun updateCustomerTransaction(
    customerId: Long,
    transactionId: Long,
    description: String,
    version: Long,
    transactions: CustomersTransactionsRepository = TempDI.incomes,
    status: StatusRepository = TempDI.status,
    errorHandler: ErrorHandler = TempDI.errorHandler,
    onError: () -> Unit
) {
    errorHandler.execute {
        val result = status.execute(
            loading = Message.fromString("جاري تحديث الإيرادات ..."),
            success = Message.fromString("تم تحديث الإيرادات"),
            job = {
                transactions.update(
                    customerId = customerId,
                    transactionId = transactionId,
                    description = description,
                    version = version
                )
            }
        )

        if (result is AsyncResult.Fail) {
            onError()
            throw result.error
        }
    }
}

private suspend fun deleteIncome(
    projectIncomeId: Long,
    projectId: Long,
    projects: ProjectsRepository = TempDI.projects,
    status: StatusRepository = TempDI.status,
    errorHandler: ErrorHandler = TempDI.errorHandler,
    onError: () -> Unit
) {
    errorHandler.execute {
        val result = status.execute(
            loading = Message.fromString("جاري حذف التحويل ..."),
            success = Message.fromString("تم حذف التحويل"),
            job = {
                projects.deleteProjectIncome(
                    projectIncomeId = projectIncomeId,
                    projectId = projectId
                )
            }
        )

        if (result is AsyncResult.Fail) {
            onError()
            throw result.error
        }
    }
}
