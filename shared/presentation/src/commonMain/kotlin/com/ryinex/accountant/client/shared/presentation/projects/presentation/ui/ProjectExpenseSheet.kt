package com.ryinex.accountant.client.shared.presentation.projects.presentation.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.ic_cancel
import com.ryinex.accountant.client.resources.ic_mic
import com.ryinex.accountant.client.shared.data.beneficiaries.models.Beneficiary
import com.ryinex.accountant.client.shared.data.common.utilities.AppDouble
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.data.common.utilities.app
import com.ryinex.accountant.client.shared.data.projects.models.Project
import com.ryinex.accountant.client.shared.data.projects.models.ProjectExpense
import com.ryinex.accountant.client.shared.data.projects.models.UpdateProjectExpenseRequest
import com.ryinex.accountant.client.shared.data.terms.models.Term
import com.ryinex.accountant.client.shared.presentation.common.DateTimePicker
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.checkbox.AppCheckbox
import com.ryinex.accountant.client.shared.presentation.common.containers.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.containers.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.containers.AppRow
import com.ryinex.accountant.client.shared.presentation.common.dialogs.DelayConfirmDialog
import com.ryinex.accountant.client.shared.presentation.common.dialogs.VoiceDialog
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.images.AppSupportImageViewButton
import com.ryinex.accountant.client.shared.presentation.common.state.BooleanState
import com.ryinex.accountant.client.shared.presentation.common.state.DerivedState
import com.ryinex.accountant.client.shared.presentation.common.state.ListState
import com.ryinex.accountant.client.shared.presentation.common.state.MutableItemState
import com.ryinex.accountant.client.shared.presentation.common.state.StringState
import com.ryinex.accountant.client.shared.presentation.common.surface.AppBottomSheet
import com.ryinex.accountant.client.shared.presentation.common.texts.AppBodyText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppDecimalTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledBodyTextHorizontal
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSelectableAutoCompleteTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTextField
import com.ryinex.accountant.client.shared.presentation.terms.AddTermSheet
import core.common.extensions.dateTime
import core.common.extensions.format
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.execute
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toInstant
import kotlinx.datetime.toLocalDateTime
import org.jetbrains.compose.resources.painterResource
import kotlin.time.Duration.Companion.seconds

private class StateHolder(
    val amount: MutableItemState<AppDouble> = MutableItemState<AppDouble>(0.0.app()),
    val description: StringState = StringState(""),

    val beneficiary: MutableItemState<Beneficiary?> = MutableItemState(null),
    val beneficiaryText: StringState = StringState(""),
    val generalBeneficiary: BooleanState = BooleanState(false),

    val term: MutableItemState<Term?> = MutableItemState(null),
    val termText: StringState = StringState(""),
    val generalTerm: BooleanState = BooleanState(false),

    val project: MutableItemState<Project?> = MutableItemState(null),
    val projectText: StringState = StringState(""),

    val beneficiaries: ListState<Beneficiary> = ListState(emptyList()),
    val terms: ListState<Term> = ListState(emptyList()),
    val projects: ListState<Project> = ListState(emptyList()),

    val transactionDate: MutableItemState<LocalDateTime> = MutableItemState(
        Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
    ),
    val isLoading: BooleanState = BooleanState(false)
) {
    val isAmountError = DerivedState(false, amount.stream.map { it == 0.0.app() })

    private val actionEnabledStream = com.ryinex.accountant.client.shared.data.common.utilities.combine(
        isAmountError.stream,
        beneficiary.stream,
        generalBeneficiary.stream,
        term.stream,
        generalTerm.stream,
        project.stream,
        isLoading.stream
    ) { amountError, beneficiary, generalBeneficiary, term, generalTerm, project, isLoading ->
        val validBeneficiary = beneficiary != null || generalBeneficiary
        val validTerm = term != null || generalTerm

        !isLoading && !amountError && validBeneficiary && validTerm && project != null
    }

    val actionEnabled = DerivedState(false, actionEnabledStream)


    fun fromExpense(expense: ProjectExpense) {
        amount.update(expense.beneficiaryTransaction.amount)
        description.update(expense.beneficiaryTransaction.description)
        beneficiary.update(expense.beneficiary)
        generalBeneficiary.update(expense.beneficiary == null)
        term.update(expense.term)
        generalTerm.update(expense.term == null)
        transactionDate.update(expense.beneficiaryTransaction.transactionDate.dateTime())
        project.update(expense.project)

    }
}


@Composable
internal fun ProjectExpenseSheet(
    project: Project?,
    expense: ProjectExpense?,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    val scope = rememberCoroutineScope()
    val state = remember { StateHolder() }

    LaunchedEffect(Unit) {
        if (expense != null) state.fromExpense(expense)
        if (project != null) state.project.update(project)
        launch { state.beneficiaries.replaceAll(getBeneficiaries(state)) }
        launch { state.terms.replaceAll(getTerms(state)) }
        launch { state.projects.replaceAll(getProjects(state)) }
    }

    AppBottomSheet(onDismissRequest = onDismissRequest) {
        AppLazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            SheetContent(
                state = state,
                expense = expense,
                onDismissRequest = onDismissRequest,
                onFinish = onFinish,
                onAddNewTerm = { scope.launch { state.terms.replaceAll(getTerms(state)) } },
            )
        }
    }
}


private fun LazyListScope.SheetContent(
    state: StateHolder,
    expense: ProjectExpense?,
    onAddNewTerm: () -> Unit,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    item {
        val title = if (expense != null) "تعديل مصاريف" else "إضافة مصاريف"
        AppSectionTitleText(title)
    }

    item { ActionsRow(state = state, expense = expense, onDismissRequest = onDismissRequest, onFinish = onFinish) }

    item { AmountInput(state = state) }

    item { DescriptionInput(state = state) }

    item { TermInput(state = state, terms = state.terms.listen, onFinish = onAddNewTerm) }

    item { BeneficiaryInput(state = state, beneficiaries = state.beneficiaries.listen) }

    item { ProjectInput(state = state, projects = state.projects.listen) }

    item { DateInput(state = state) }
}

@Composable
private fun AmountInput(state: StateHolder) {
    AppDecimalTextField(
        modifier = Modifier.fillMaxWidth(),
        amount = state.amount.listen,
        hint = "القيمة",
        enabled = !state.isLoading.listen,
        isError = state.isAmountError.listen,
        onlyPositive = true,
        onValueChange = { state.amount.update(it) },
    )
}

@Composable
private fun DescriptionInput(state: StateHolder) {
    AppTextField(
        modifier = Modifier.fillMaxWidth(),
        text = state.description.listen,
        hint = "الوصف",
        enabled = !state.isLoading.listen,
        isError = false,
        onValueChange = { state.description.update(it) },
    )
}

@Composable
private fun TermInput(state: StateHolder, terms: List<Term>, onFinish: () -> Unit) {
    val showAddSheet = remember { mutableStateOf(false) }

    AppRow(
        modifier = Modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
        alignment = Alignment.CenterVertically
    ) {
        if (!state.generalTerm.listen) {
            Box(modifier = Modifier.weight(1f)) {
                AppSelectableAutoCompleteTextField(
                    modifier = Modifier.fillMaxWidth(),
                    text = state.termText.listen,
                    hint = "البند",
                    enabled = !state.isLoading.listen,
                    isError = false,
                    minSuggestLength = 0,
                    errorText = "",
                    allItems = terms,
                    searchMapper = { "${it.name} - ${it.termsGroup.name}" },
                    displayMapper = { "${it.name} - (${it.termsGroup.name})" },
                    onItemClick = { state.term.update(it) },
                    onEmptyItemClick = {
                        state.term.update(null)
                        showAddSheet.value = true
                    },
                    items = terms,
                    onValueChange = { state.termText.update(it) },
                    selected = state.term.listen,
                    onCancelSelected = { state.term.update(null) }
                )
            }
        } else {
            AppBodyText("البند")
        }

        if (state.term.listen == null) {
            AppCheckbox(
                label = "عام",
                checked = state.generalTerm.listen,
                onCheckedChange = { state.generalTerm.update(it) }
            )
        }
    }

    if (showAddSheet.value) {
        AddTermSheet(
            termsGroup = null,
            groups = terms.map { it.termsGroup }.distinctBy { it.id },
            isCancelTermsGroupEnabled = true,
            onDismissRequest = { showAddSheet.value = false },
            onFinish = { onFinish() }
        )
    }
}

@Composable
private fun BeneficiaryInput(state: StateHolder, beneficiaries: List<Beneficiary>) {
    val showAddSheet = remember { mutableStateOf(false) }

    AppRow(
        modifier = Modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
        alignment = Alignment.CenterVertically
    ) {
        if (!state.generalBeneficiary.listen) {
            Box(modifier = Modifier.weight(1f)) {
                AppSelectableAutoCompleteTextField(
                    modifier = Modifier.fillMaxWidth(),
                    text = state.beneficiaryText.listen,
                    hint = "المستفيد",
                    enabled = !state.isLoading.listen,
                    isError = false,
                    minSuggestLength = 0,
                    errorText = "",
                    allItems = beneficiaries,
                    searchMapper = { it.name },
                    displayMapper = { it.name },
                    onItemClick = { state.beneficiary.update(it) },
                    onEmptyItemClick = {
                        state.beneficiaryText.update("")
                        showAddSheet.value = true
                    },
                    items = beneficiaries,
                    onValueChange = { state.beneficiaryText.update(it) },
                    selected = state.beneficiary.listen,
                    onCancelSelected = { state.beneficiary.update(null) }
                )
            }
        } else {
            AppBodyText("المستفيد")
        }

        if (state.beneficiary.listen == null) {
            AppCheckbox(
                label = "عام",
                checked = state.generalBeneficiary.listen,
                onCheckedChange = { state.generalBeneficiary.update(it) }
            )
        }
    }

    if (showAddSheet.value) {
        TODO()
    }
}

@Composable
private fun ProjectInput(state: StateHolder, projects: List<Project>) {
    AppSelectableAutoCompleteTextField(
        modifier = Modifier.fillMaxWidth(),
        text = state.projectText.listen,
        hint = "المشروع",
        enabled = !state.isLoading.listen,
        isError = false,
        minSuggestLength = 0,
        errorText = "",
        allItems = projects,
        searchMapper = { it.name },
        displayMapper = { it.name },
        onItemClick = { state.project.update(it) },
        onEmptyItemClick = { state.projectText.update("") },
        items = projects,
        onValueChange = { state.projectText.update(it) },
        selected = state.project.listen,
        onCancelSelected = { state.project.update(null) },
        isCancelSelectedEnabled = true
    )
}

@Composable
private fun DateInput(state: StateHolder) {
    AppColumn(
        modifier = Modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.Top),
        alignment = Alignment.Start
    ) {
        DateTimePicker(
            modifier = Modifier.fillMaxWidth(),
            now = state.transactionDate.listen,
            onPicked = { state.transactionDate.update(it) }
        )

        AppLabeledBodyTextHorizontal(label = "التاريخ", text = state.transactionDate.listen.format())
    }
}


@Composable
private fun ActionsRow(
    modifier: Modifier = Modifier,
    state: StateHolder,
    expense: ProjectExpense?,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    var showDeleteDialog by remember { mutableStateOf(false) }
    val scope = rememberCoroutineScope()

    AppRow(
        modifier = modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
        alignment = Alignment.CenterVertically
    ) {
        if (expense != null) {
            AppTextButton(
                modifier = Modifier,
                text = "حذف",
                enabled = !state.isLoading.listen,
                color = MaterialTheme.colorScheme.error,
                iconPainter = painterResource(Res.drawable.ic_cancel),
                onClick = { showDeleteDialog = true }
            )
        }

        AppRow(
            modifier = modifier.weight(1f),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
            alignment = Alignment.CenterVertically
        ) {
            VoiceRecordButtons { audioBytes -> scope.launch { voiceFillForm(state, audioBytes) } }

            AppTextButton(text = "إلغاء", enabled = !state.isLoading.listen, onClick = { onDismissRequest() })

            AppTextButton(
                text = if (expense != null) "تعديل" else "إضافة",
                enabled = state.actionEnabled.listen,
                onClick = {
                    scope.launch {
                        if (expense != null) updateExpense(state, expense, onFinish) else addExpense(state, onFinish)
                    }
                }
            )
        }
    }

    if (showDeleteDialog) {
        DelayConfirmDialog(
            title = "حذف المصاريف",
            message = "هل أنت متأكد من حذف المصاريف ؟",
            onConfirm = {
                scope.launch {
                    deleteExpense(
                        projectExpenseId = expense!!.id,
                        projectId = expense.project.id,
                        onError = { showDeleteDialog = false }
                    )
                    showDeleteDialog = false
                    onFinish()
                }
            },
            onDismiss = { showDeleteDialog = false },
            confirmContainerColor = MaterialTheme.colorScheme.error,
            confirmTextColor = MaterialTheme.colorScheme.onError,
            delay = 5.seconds
        )
    }
}


@Composable
private fun VoiceRecordButtons(
    onRecord: (ByteArray) -> Unit
) {
    var showDialog by remember { mutableStateOf(false) }

    AppSupportImageViewButton(
        enabled = true,
        painter = painterResource(Res.drawable.ic_mic),
        onClick = { showDialog = true },
    )

    if (showDialog) {
        VoiceDialog(
            bestResults = "لأفضل نتيجة تحدث بهذه الصيغة:\nتم دفع قيمة .... المستفيد .... تحت بند .... الوصف ....",
            onDismiss = { showDialog = false },
            onFinish = onRecord
        )
    }
}

private suspend fun voiceFillForm(state: StateHolder, audioBytes: ByteArray) {
    jobWrapper(
        state = state,
        loading = "جاري تحليل الصوت ...",
        success = "تم تحليل الصوت",
        job = { voiceFillFormJob(state, audioBytes) }
    )
}

private suspend fun voiceFillFormJob(state: StateHolder, audioBytes: ByteArray) {
    val prompt = """
            key: transactionAmount -> description: إجمالي المبلغ المدفوع كأرقام فقط بدون أي وحدات  على هيئة string
            key: beneficiary -> description: the entity whom will receive the money
            key: project -> description: for what project or job this transaction is being made
            key: notes -> description: any notes or description mentioned in input
            key: term -> description: which specific summarized term (بند) of the project terms that this money is spent in
        """.trimIndent()
    val response = TempDI.projects.expensesForm(prompt, audioBytes)

    if (response.transactionAmount != null && response.transactionAmount!!.toDoubleOrNull() != null) {
        state.amount.update(response.transactionAmount!!.toDouble().app())
    }
    if (response.notes != null) state.description.update(response.notes!!)
    if (response.beneficiary != null) state.beneficiaryText.update(response.beneficiary!!)
    if (response.term != null) state.termText.update(response.term!!)
}

private suspend fun addExpense(
    state: StateHolder,
    onFinish: () -> Unit
) {
    jobWrapper(
        state = state,
        loading = "جاري إضافة المصاريف ...",
        success = "تم إضافة المصاريف",
        job = { addExpenseJob(projectId = state.project.get()!!.id, state = state, onFinish = onFinish) }
    )
}

private suspend fun addExpenseJob(
    projectId: Long,
    state: StateHolder,
    onFinish: () -> Unit
) {
    TempDI.projects.addExpense(
        projectId = projectId,
        beneficiaryId = state.beneficiary.get()?.id,
        termId = state.term.get()?.id,
        termsGroupId = state.term.get()?.termsGroup?.id,
        amount = state.amount.get(),
        description = state.description.get(),
        transactionDateIseoString = state.transactionDate.get().toInstant(TimeZone.currentSystemDefault()).toString()
    )

    onFinish()
}

private suspend fun updateExpense(
    state: StateHolder,
    expense: ProjectExpense,
    onFinish: () -> Unit
) {
    jobWrapper(
        state = state,
        loading = "جاري تعديل المصاريف ...",
        success = "تم تعديل المصاريف",
        job = { updateExpenseJob(expense = expense, state = state, onFinish = onFinish) }
    )
}

private suspend fun updateExpenseJob(
    expense: ProjectExpense,
    state: StateHolder,
    onFinish: () -> Unit
) {
    val request = UpdateProjectExpenseRequest(
        amount = state.amount.get().toDouble(),
        amountPaid = state.amount.get().toDouble(),
        description = state.description.get(),
        transactionDate = state.transactionDate.get().toInstant(TimeZone.currentSystemDefault()).toString(),
        projectId = state.project.get()!!.id,
        beneficiaryId = state.beneficiary.get()?.id,
        termsGroupId = state.term.get()?.termsGroup?.id,
        termId = state.term.get()?.id,
        version = expense.version,
        transactionVersion = expense.beneficiaryTransaction.version
    )

    TempDI.projects.updateProjectExpense(expenseId = expense.id, request = request)

    onFinish()
}

private suspend fun deleteExpense(
    projectExpenseId: Long,
    projectId: Long,
    onError: () -> Unit
) {
    jobWrapper(
        state = StateHolder(),
        loading = "جاري حذف المصاريف ...",
        success = "تم حذف المصاريف",
        job = { TempDI.projects.deleteProjectExpense(projectExpenseId = projectExpenseId, projectId = projectId) }
    ) ?: onError()
}

private suspend fun getBeneficiaries(state: StateHolder): List<Beneficiary> {
    return jobWrapper(
        state = state,
        loading = "جاري تحميل المستفيدين ...",
        success = "تم تحميل المستفيدين",
        job = { TempDI.beneficiaries.getBeneficiariesOfOrganization() }
    ) ?: emptyList()
}

private suspend fun getTerms(state: StateHolder): List<Term> {
    return jobWrapper(
        state = state,
        loading = "جاري تحميل بنود المشروع ...",
        success = "تم تحميل بنود المشروع",
        job = { TempDI.terms.getTermsOfOrganization() }
    ) ?: emptyList()
}

private suspend fun getProjects(state: StateHolder): List<Project> {
    return jobWrapper(
        state = state,
        loading = "جاري تحميل المشاريع ...",
        success = "تم تحميل المشاريع",
        job = { TempDI.projects.getProjectsOfOrganization() }
    ) ?: emptyList()
}

private suspend fun <T> jobWrapper(state: StateHolder, loading: String, success: String, job: suspend () -> T): T? {
    val status = TempDI.status
    val errorHolder = TempDI.errorHandler

    return errorHolder.execute {
        state.isLoading.update(true)

        val result = status.execute(
            loading = Message.fromString(loading),
            success = Message.fromString(success),
            job = job
        )

        state.isLoading.update(false)

        return@execute if (result is AsyncResult.Fail) throw result.error else result.result
    }
}
