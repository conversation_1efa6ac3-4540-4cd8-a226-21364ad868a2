package com.ryinex.accountant.client.shared.presentation.terms

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.data.terms.models.TermsGroup
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.containers.AppRow
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.state.BooleanState
import com.ryinex.accountant.client.shared.presentation.common.state.DerivedState
import com.ryinex.accountant.client.shared.presentation.common.state.StringState
import com.ryinex.accountant.client.shared.presentation.common.surface.AppBottomSheet
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTextField
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.execute
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch

private class TermsGroupStateHolder(
    val name: StringState = StringState(""),
    val description: StringState = StringState(""),
    val isLoading: BooleanState = BooleanState(false)
) {
    private val minNameLength = 3
    private val minDescriptionLength = 3

    val isNameError = DerivedState(true, name.stream.map { it.trim().length < minNameLength })
    val isDescriptionError = DerivedState(true, description.stream.map { it.trim().length < minDescriptionLength })

    private val actionEnabledStream = combine(
        isNameError.stream,
        isDescriptionError.stream,
        isLoading.stream
    ) { nameError, descriptionError, loading ->
        !loading && !nameError && !descriptionError
    }

    val actionEnabled = DerivedState(false, actionEnabledStream)

    fun fromTermsGroup(termsGroup: TermsGroup) {
        name.update(termsGroup.name)
        description.update(termsGroup.description)
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TermsGroupSheet(
    termsGroup: TermsGroup? = null,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    val state = remember { TermsGroupStateHolder() }

    LaunchedEffect(Unit) {
        if (termsGroup != null) state.fromTermsGroup(termsGroup)
    }

    AppBottomSheet(onDismissRequest = onDismissRequest) {
        AppLazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            SheetContent(
                state = state,
                termsGroup = termsGroup,
                onDismissRequest = onDismissRequest,
                onFinish = onFinish
            )
        }
    }
}

private fun LazyListScope.SheetContent(
    state: TermsGroupStateHolder,
    termsGroup: TermsGroup?,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    item {
        val title = if (termsGroup != null) "تعديل مجموعة بنود أعمال" else "إضافة مجموعة بنود أعمال"
        AppSectionTitleText(title)
    }

    item { ActionsRow(state = state, termsGroup = termsGroup, onDismissRequest = onDismissRequest, onFinish = onFinish) }

    item { NameInput(state = state) }

    item { DescriptionInput(state = state) }
}

@Composable
private fun NameInput(state: TermsGroupStateHolder) {
    AppTextField(
        modifier = Modifier.fillMaxWidth(),
        text = state.name.listen,
        hint = "الاسم",
        enabled = !state.isLoading.listen,
        isError = state.isNameError.listen,
        onValueChange = { state.name.update(it) },
        errorText = "المجموعة يجب أن يكون على الأقل 3 أحرف"
    )
}

@Composable
private fun DescriptionInput(state: TermsGroupStateHolder) {
    AppTextField(
        modifier = Modifier.fillMaxWidth(),
        text = state.description.listen,
        hint = "الوصف",
        enabled = !state.isLoading.listen,
        isError = state.isDescriptionError.listen,
        onValueChange = { state.description.update(it) },
        errorText = "الوصف يجب أن يكون على الأقل 3 أحرف"
    )
}

@Composable
private fun ActionsRow(
    modifier: Modifier = Modifier,
    state: TermsGroupStateHolder,
    termsGroup: TermsGroup?,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    val scope = rememberCoroutineScope()

    AppRow(
        modifier = modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
        alignment = Alignment.CenterVertically
    ) {
        AppTextButton(
            modifier = Modifier,
            text = "إلغاء",
            enabled = !state.isLoading.listen,
            onClick = onDismissRequest
        )

        AppTextButton(
            modifier = Modifier,
            text = if (termsGroup != null) "تعديل" else "إضافة",
            enabled = state.actionEnabled.listen,
            onClick = {
                scope.launch {
                    if (termsGroup != null) {
                        updateTermsGroup(state = state, termsGroup = termsGroup, onFinish = onFinish)
                    } else {
                        addTermsGroup(state = state, onFinish = onFinish)
                    }
                    onDismissRequest()
                }
            }
        )
    }
}

private suspend fun addTermsGroup(
    state: TermsGroupStateHolder,
    onFinish: () -> Unit
) {
    jobWrapper(
        state = state,
        loading = "جاري إضافة مجموعة بنود أعمال ...",
        success = "تم إضافة مجموعة بنود أعمال",
        job = { addTermsGroupJob(state = state, onFinish = onFinish) }
    )
}

private suspend fun addTermsGroupJob(
    state: TermsGroupStateHolder,
    onFinish: () -> Unit
) {
    TempDI.termsGroups.create(
        name = state.name.get(),
        description = state.description.get()
    )

    onFinish()
}

private suspend fun updateTermsGroup(
    state: TermsGroupStateHolder,
    termsGroup: TermsGroup,
    onFinish: () -> Unit
) {
    jobWrapper(
        state = state,
        loading = "جاري تعديل مجموعة بنود أعمال ...",
        success = "تم تعديل مجموعة بنود أعمال",
        job = { updateTermsGroupJob(termsGroup = termsGroup, state = state, onFinish = onFinish) }
    )
}

private suspend fun updateTermsGroupJob(
    termsGroup: TermsGroup,
    state: TermsGroupStateHolder,
    onFinish: () -> Unit
) {
    TempDI.termsGroups.edit(
        termsGroupId = termsGroup.id,
        name = state.name.get(),
        description = state.description.get(),
        version = termsGroup.version
    )

    onFinish()
}

private suspend fun <T> jobWrapper(state: TermsGroupStateHolder, loading: String, success: String, job: suspend () -> T): T? {
    val status = TempDI.status
    val errorHolder = TempDI.errorHandler

    return errorHolder.execute {
        state.isLoading.update(true)

        val result = status.execute(
            loading = Message.fromString(loading),
            success = Message.fromString(success),
            job = job
        )

        state.isLoading.update(false)

        return@execute if (result is AsyncResult.Fail) throw result.error else result.result
    }
}

// Backward compatibility function
@Deprecated("Use TermsGroupSheet instead", ReplaceWith("TermsGroupSheet(termsGroup = if (isEditing) TermsGroup(...) else null, onDismissRequest = onDismissRequest, onFinish = onFinish)"))
@Composable
fun AddTermsGroupSheet(
    initialName: String,
    initialDescription: String,
    termsGroupId: Long?,
    version: Long?,
    isEditing: Boolean,
    onFinish: () -> Unit,
    onDismissRequest: () -> Unit
) {
    // For backward compatibility, we'll create a temporary termsGroup object if editing
    val termsGroup = if (isEditing && termsGroupId != null && version != null) {
        TermsGroup(
            id = termsGroupId,
            organizationId = 0L,
            name = initialName,
            description = initialDescription,
            createdAt = "",
            createdBy = 0L,
            updatedAt = "",
            updatedBy = 0L,
            version = version
        )
    } else null

    TermsGroupSheet(
        termsGroup = termsGroup,
        onDismissRequest = onDismissRequest,
        onFinish = onFinish
    )
}
