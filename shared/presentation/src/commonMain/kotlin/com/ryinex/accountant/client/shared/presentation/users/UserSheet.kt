package com.ryinex.accountant.client.shared.presentation.users

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.data.users.models.User
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.checkbox.AppCheckbox
import com.ryinex.accountant.client.shared.presentation.common.containers.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.containers.AppRow
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.state.BooleanState
import com.ryinex.accountant.client.shared.presentation.common.state.DerivedState
import com.ryinex.accountant.client.shared.presentation.common.state.StringState
import com.ryinex.accountant.client.shared.presentation.common.surface.AppBottomSheet
import com.ryinex.accountant.client.shared.presentation.common.texts.AppPhoneNumberTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTextField
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.execute
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch

private class UserStateHolder(
    val name: StringState = StringState(""),
    val phoneNumber: StringState = StringState(""),
    val email: StringState = StringState(""),
    val password: StringState = StringState(""),
    val isAdmin: BooleanState = BooleanState(false),
    val isLoading: BooleanState = BooleanState(false)
) {
    private val minNameLength = 3
    private val minPhoneNumberLength = 11
    private val minEmailLength = 5
    private val minPasswordLength = 6

    val isNameError = DerivedState(true, name.stream.map { it.trim().length < minNameLength })
    val isPhoneNumberError = DerivedState(true, phoneNumber.stream.map { it.trim().length < minPhoneNumberLength })
    val isEmailError = DerivedState(true, email.stream.map { 
        val trimmed = it.trim()
        trimmed.length < minEmailLength || !trimmed.contains("@") || !trimmed.contains(".")
    })
    val isPasswordError = DerivedState(true, password.stream.map { it.trim().length < minPasswordLength })

    private val actionEnabledStream = combine(
        isNameError.stream,
        isPhoneNumberError.stream,
        isEmailError.stream,
        isPasswordError.stream,
        isLoading.stream
    ) { nameError, phoneError, emailError, passwordError, loading ->
        !loading && !nameError && !phoneError && !emailError && !passwordError
    }

    val actionEnabled = DerivedState(false, actionEnabledStream)

    fun fromUser(user: User) {
        name.update(user.name)
        phoneNumber.update(user.phoneNumber)
        email.update(user.email)
        isAdmin.update(user.isAdmin)
        // Don't populate password for security reasons
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UserSheet(
    user: User? = null,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    val scope = rememberCoroutineScope()
    val state = remember { UserStateHolder() }

    LaunchedEffect(Unit) {
        if (user != null) state.fromUser(user)
    }

    AppBottomSheet(onDismissRequest = onDismissRequest) {
        AppLazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            SheetContent(
                state = state,
                user = user,
                onDismissRequest = onDismissRequest,
                onFinish = onFinish
            )
        }
    }
}

private fun LazyListScope.SheetContent(
    state: UserStateHolder,
    user: User?,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    item {
        val title = if (user != null) "تعديل مستخدم" else "إضافة مستخدم"
        AppSectionTitleText(title)
    }

    item { ActionsRow(state = state, user = user, onDismissRequest = onDismissRequest, onFinish = onFinish) }

    item { NameInput(state = state) }

    item { PhoneNumberInput(state = state) }

    item { EmailInput(state = state) }

    if (user == null) {
        item { PasswordInput(state = state) }
    }

    item { AdminCheckbox(state = state) }
}

@Composable
private fun NameInput(state: UserStateHolder) {
    AppTextField(
        modifier = Modifier.fillMaxWidth(),
        text = state.name.listen,
        hint = "الاسم",
        enabled = !state.isLoading.listen,
        isError = state.isNameError.listen,
        onValueChange = { state.name.update(it) },
        errorText = "الاسم يجب أن يكون على الأقل 3 أحرف"
    )
}

@Composable
private fun PhoneNumberInput(state: UserStateHolder) {
    AppPhoneNumberTextField(
        modifier = Modifier.fillMaxWidth(),
        phoneNumber = state.phoneNumber.listen,
        enabled = !state.isLoading.listen,
        isError = state.isPhoneNumberError.listen,
        phoneNumberLength = 11,
        onValueChange = { state.phoneNumber.update(it) }
    )
}

@Composable
private fun EmailInput(state: UserStateHolder) {
    AppTextField(
        modifier = Modifier.fillMaxWidth(),
        text = state.email.listen,
        hint = "البريد الإلكتروني",
        enabled = !state.isLoading.listen,
        isError = state.isEmailError.listen,
        onValueChange = { state.email.update(it) },
        errorText = "البريد الإلكتروني غير صحيح"
    )
}

@Composable
private fun PasswordInput(state: UserStateHolder) {
    AppTextField(
        modifier = Modifier.fillMaxWidth(),
        text = state.password.listen,
        hint = "كلمة المرور",
        enabled = !state.isLoading.listen,
        isError = state.isPasswordError.listen,
        onValueChange = { state.password.update(it) },
        errorText = "كلمة المرور يجب أن تكون على الأقل 6 أحرف"
    )
}

@Composable
private fun AdminCheckbox(state: UserStateHolder) {
    AppCheckbox(
        label = "مدير",
        checked = state.isAdmin.listen,
        enabled = !state.isLoading.listen,
        onCheckedChange = { state.isAdmin.update(it) }
    )
}

@Composable
private fun ActionsRow(
    modifier: Modifier = Modifier,
    state: UserStateHolder,
    user: User?,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    val scope = rememberCoroutineScope()

    AppRow(
        modifier = modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
        alignment = Alignment.CenterVertically
    ) {
        AppTextButton(
            modifier = Modifier,
            text = "إلغاء",
            enabled = !state.isLoading.listen,
            onClick = onDismissRequest
        )

        AppTextButton(
            modifier = Modifier,
            text = if (user != null) "تعديل" else "إضافة",
            enabled = state.actionEnabled.listen,
            onClick = {
                scope.launch {
                    if (user != null) {
                        updateUser(state = state, user = user, onFinish = onFinish)
                    } else {
                        addUser(state = state, onFinish = onFinish)
                    }
                    onDismissRequest()
                }
            }
        )
    }
}

private suspend fun addUser(
    state: UserStateHolder,
    onFinish: () -> Unit
) {
    jobWrapper(
        state = state,
        loading = "جاري إضافة المستخدم ...",
        success = "تم إضافة المستخدم",
        job = { addUserJob(state = state, onFinish = onFinish) }
    )
}

private suspend fun addUserJob(
    state: UserStateHolder,
    onFinish: () -> Unit
) {
    TempDI.users.createUser(
        name = state.name.get(),
        phoneNumber = state.phoneNumber.get(),
        password = state.password.get(),
        isAdmin = state.isAdmin.get()
    )

    onFinish()
}

private suspend fun updateUser(
    state: UserStateHolder,
    user: User,
    onFinish: () -> Unit
) {
    jobWrapper(
        state = state,
        loading = "جاري تعديل المستخدم ...",
        success = "تم تعديل المستخدم",
        job = { updateUserJob(user = user, state = state, onFinish = onFinish) }
    )
}

private suspend fun updateUserJob(
    user: User,
    state: UserStateHolder,
    onFinish: () -> Unit
) {
    TempDI.users.editUser(
        userId = user.id,
        name = state.name.get(),
        phoneNumber = state.phoneNumber.get(),
        email = state.email.get(),
        isAdmin = state.isAdmin.get(),
        version = user.version
    )

    onFinish()
}

private suspend fun <T> jobWrapper(state: UserStateHolder, loading: String, success: String, job: suspend () -> T): T? {
    val status = TempDI.status
    val errorHolder = TempDI.errorHandler

    return errorHolder.execute {
        state.isLoading.update(true)

        val result = status.execute(
            loading = Message.fromString(loading),
            success = Message.fromString(success),
            job = job
        )

        state.isLoading.update(false)

        return@execute if (result is AsyncResult.Fail) throw result.error else result.result
    }
}
