package com.ryinex.accountant.client.shared.presentation.customers.presentation.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.ModalBottomSheetDefaults
import androidx.compose.material3.SheetValue
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import cafe.adriel.voyager.navigator.LocalNavigator
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppCard
import com.ryinex.accountant.client.shared.presentation.common.containers.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.containers.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.containers.AppRow
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.state.BooleanState
import com.ryinex.accountant.client.shared.presentation.common.state.DerivedState
import com.ryinex.accountant.client.shared.presentation.common.state.StringState
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledBodyTextHorizontal
import com.ryinex.accountant.client.shared.presentation.common.texts.AppPhoneNumberTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSubSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTextField
import com.ryinex.accountant.client.shared.presentation.home.presentation.ui.NextArrowButton
import com.ryinex.accountant.client.shared.presentation.theme.LocalIsDesktop
import com.ryinex.accountant.client.shared.presentation.utils.browserPush
import com.ryinex.accountant.client.shared.data.customers.models.Customer
import com.ryinex.accountant.client.shared.data.customers.repositories.CustomersRepository
import com.ryinex.accountant.client.shared.domain.customers.home.ViewModel
import com.ryinex.accountant.client.shared.domain.customers.home.models.ScreenState
import com.ryinex.accountant.client.shared.data.common.error.ErrorHandler
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.presentation.common.shadowBluish
import com.ryinex.accountant.client.shared.presentation.common.surface.AppBottomSheet
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import kotlinx.coroutines.launch

internal fun LazyStaggeredGridScope.CustomersListSection(
    customers: List<Customer>,
    viewModel: ViewModel,
    state: ScreenState
) {
    state.customers.forEach { customer ->
        item {
            val navigator = LocalNavigator.current

            CustomerCard(
                modifier = Modifier.then(
                    if (LocalIsDesktop.current) Modifier.width(Dimensions.Cards.maxCardWidthDesktop)
                    else Modifier.fillMaxWidth()
                ),
                name = customer.name,
                phoneNumber = customer.phoneNumber,
                enabled = state.isCustomerDetailsEnabled,
                onClick = { navigator?.browserPush(CustomerTransactionsScreen(customer)) }
            )
        }
    }
}

@Composable
fun CustomerCard(
    modifier: Modifier = Modifier,
    name: String,
    phoneNumber: String,
    enabled: Boolean,
    onClick: () -> Unit
) {
    AppCard(
        modifier = modifier.fillMaxWidth().shadowBluish(),
        enabled = enabled,
        onClick = onClick,
        padding = Dimensions.Paddings.medium.dp,
        regularColors = true
    ) {
        AppRow(
            modifier = Modifier.fillMaxWidth(),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
            alignment = Alignment.CenterVertically
        ) {
            AppColumn(
                modifier = Modifier.weight(1f),
                arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.Top),
                alignment = Alignment.Start
            ) {
                AppSubSectionTitleText(text = name, color = MaterialTheme.colorScheme.primary)

                AppLabeledBodyTextHorizontal(label = "الهاتف", text = phoneNumber)
            }

            NextArrowButton(
                enabled = enabled,
                onClick = onClick
            )
        }
    }
}

private class StateHolder(
    val name: StringState = StringState(""),
    val phoneNumber: StringState = StringState(""),
    val secondaryPhoneNumber: StringState = StringState(""),
    val isLoading: BooleanState = BooleanState(false)
) {
    private val minNameLength = 3
    private val minPhoneNumberLength = 11

    val isNameError = DerivedState(true, name.stream.map { it.trim().length < minNameLength })
    val isPhoneNumberError = DerivedState(true, phoneNumber.stream.map { it.trim().length < minPhoneNumberLength })

    private val actionEnabledStream = com.ryinex.accountant.client.shared.data.common.utilities.combine(
        isNameError.stream,
        isPhoneNumberError.stream,
        isLoading.stream
    ) { nameError, phoneError, loading ->
        !loading && !nameError && !phoneError
    }

    val actionEnabled = DerivedState(false, actionEnabledStream)

    fun fromCustomer(customer: Customer) {
        name.update(customer.name)
        phoneNumber.update(customer.phoneNumber)
        secondaryPhoneNumber.update(customer.secondaryPhoneNumber)
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CustomerSheet(
    customer: Customer? = null,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    val scope = rememberCoroutineScope()
    val state = remember { StateHolder() }

    LaunchedEffect(Unit) {
        if (customer != null) state.fromCustomer(customer)
    }

    AppBottomSheet(onDismissRequest = onDismissRequest) {
        AppLazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            SheetContent(
                state = state,
                customer = customer,
                onDismissRequest = onDismissRequest,
                onFinish = onFinish
            )
        }
    }
}

private fun LazyListScope.SheetContent(
    state: StateHolder,
    customer: Customer?,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    item {
        val title = if (customer != null) "تعديل عميل" else "إضافة عميل"
        AppSectionTitleText(title)
    }

    item { ActionsRow(state = state, customer = customer, onDismissRequest = onDismissRequest, onFinish = onFinish) }

    item { NameInput(state = state) }

    item { PhoneNumberInput(state = state) }

    item { SecondaryPhoneNumberInput(state = state) }
}

// Backward compatibility function
@Deprecated("Use CustomerSheet instead", ReplaceWith("CustomerSheet(customer = if (isEditing) Customer(...) else null, onDismissRequest = onDismissRequest, onFinish = onFinish)"))
@Composable
fun AddCustomerSheet(
    initialName: String,
    initialPhoneNumber: String,
    isEditing: Boolean,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    // For backward compatibility, we'll create a temporary customer object if editing
    val customer = if (isEditing && initialName.isNotEmpty()) {
        Customer(
            id = 0L,
            organizationId = 0L,
            name = initialName,
            phoneNumber = initialPhoneNumber,
            secondaryPhoneNumber = "",
            createdAt = "",
            createdBy = 0L,
            updatedAt = "",
            updatedBy = 0L,
            version = 0L
        )
    } else null

    CustomerSheet(
        customer = customer,
        onDismissRequest = onDismissRequest,
        onFinish = onFinish
    )
}

@Composable
private fun NameInput(state: StateHolder) {
    AppTextField(
        modifier = Modifier.fillMaxWidth(),
        text = state.name.value,
        hint = "الاسم",
        enabled = !state.isLoading.value,
        isError = state.isNameError.value,
        onValueChange = { state.name.update(it) },
        errorText = "الاسم يجب أن يكون على الأقل 3 أحرف"
    )
}

@Composable
private fun PhoneNumberInput(state: StateHolder) {
    AppPhoneNumberTextField(
        modifier = Modifier.fillMaxWidth(),
        phoneNumber = state.phoneNumber.value,
        enabled = !state.isLoading.value,
        isError = state.isPhoneNumberError.value,
        phoneNumberLength = 11,
        onValueChange = { state.phoneNumber.update(it) }
    )
}

@Composable
private fun SecondaryPhoneNumberInput(state: StateHolder) {
    AppPhoneNumberTextField(
        modifier = Modifier.fillMaxWidth(),
        phoneNumber = state.secondaryPhoneNumber.value,
        enabled = !state.isLoading.value,
        isError = false,
        phoneNumberLength = 11,
        hint = "الهاتف الثانوي (اختياري)",
        onValueChange = { state.secondaryPhoneNumber.update(it) }
    )
}

@Composable
private fun ActionsRow(
    modifier: Modifier = Modifier,
    state: StateHolder,
    customer: Customer?,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    val scope = rememberCoroutineScope()

    AppRow(
        modifier = modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
        alignment = Alignment.CenterVertically
    ) {
        AppTextButton(
            modifier = Modifier,
            text = "إلغاء",
            enabled = !state.isLoading.value,
            onClick = onDismissRequest
        )

        AppTextButton(
            modifier = Modifier,
            text = if (customer != null) "تعديل" else "إضافة",
            enabled = state.actionEnabled.value,
            onClick = {
                scope.launch {
                    if (customer != null) {
                        updateCustomer(state = state, customer = customer, onFinish = onFinish)
                    } else {
                        addCustomer(state = state, onFinish = onFinish)
                    }
                    onDismissRequest()
                }
            }
        )
    }
}

private suspend fun addCustomer(
    state: StateHolder,
    onFinish: () -> Unit
) {
    jobWrapper(
        state = state,
        loading = "جاري إضافة العميل ...",
        success = "تم إضافة العميل",
        job = { addCustomerJob(state = state, onFinish = onFinish) }
    )
}

private suspend fun addCustomerJob(
    state: StateHolder,
    onFinish: () -> Unit
) {
    TempDI.customers.createCustomer(
        name = state.name.get(),
        phoneNumber = state.phoneNumber.get(),
        secondaryPhoneNumber = state.secondaryPhoneNumber.get()
    )

    onFinish()
}

private suspend fun updateCustomer(
    state: StateHolder,
    customer: Customer,
    onFinish: () -> Unit
) {
    jobWrapper(
        state = state,
        loading = "جاري تعديل العميل ...",
        success = "تم تعديل العميل",
        job = { updateCustomerJob(customer = customer, state = state, onFinish = onFinish) }
    )
}

private suspend fun updateCustomerJob(
    customer: Customer,
    state: StateHolder,
    onFinish: () -> Unit
) {
    TempDI.customers.edit(
        customerId = customer.id,
        name = state.name.get(),
        phoneNumber = state.phoneNumber.get(),
        version = customer.version
    )

    onFinish()
}

private suspend fun <T> jobWrapper(state: StateHolder, loading: String, success: String, job: suspend () -> T): T? {
    val status = TempDI.status
    val errorHolder = TempDI.errorHandler

    return errorHolder.execute {
        state.isLoading.update(true)

        val result = status.execute(
            loading = Message.fromString(loading),
            success = Message.fromString(success),
            job = job
        )

        state.isLoading.update(false)

        return@execute if (result is AsyncResult.Fail) throw result.error else result.result
    }
}