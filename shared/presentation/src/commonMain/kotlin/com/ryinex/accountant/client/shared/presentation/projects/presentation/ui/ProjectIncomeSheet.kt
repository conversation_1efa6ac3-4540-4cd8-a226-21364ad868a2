package com.ryinex.accountant.client.shared.presentation.projects.presentation.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.data.customers.models.Customer
import com.ryinex.accountant.client.shared.data.projects.models.Project
import com.ryinex.accountant.client.shared.data.projects.models.ProjectIncome
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.containers.AppRow
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.state.BooleanState
import com.ryinex.accountant.client.shared.presentation.common.state.DerivedState
import com.ryinex.accountant.client.shared.presentation.common.state.MutableItemState
import com.ryinex.accountant.client.shared.presentation.common.state.StringState
import com.ryinex.accountant.client.shared.presentation.common.surface.AppBottomSheet
import com.ryinex.accountant.client.shared.presentation.common.texts.AppDateTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppMoneyTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSelectableAutoCompleteTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTextField
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.execute
import core.common.utilities.AppDouble
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock

private class StateHolder(
    val amount: StringState = StringState(""),
    val description: StringState = StringState(""),
    val customer: MutableItemState<Customer?> = MutableItemState(null),
    val customerText: StringState = StringState(""),
    val transactionDate: StringState = StringState(""),
    val isLoading: BooleanState = BooleanState(false)
) {
    private val minDescriptionLength = 3

    val isAmountError = DerivedState(true, amount.stream.map { 
        it.trim().isEmpty() || it.toDoubleOrNull() == null || it.toDouble() <= 0 
    })
    val isDescriptionError = DerivedState(true, description.stream.map { it.trim().length < minDescriptionLength })
    val isCustomerError = DerivedState(true, customer.stream.map { it == null })
    val isTransactionDateError = DerivedState(true, transactionDate.stream.map { it.trim().isEmpty() })

    private val actionEnabledStream = com.ryinex.accountant.client.shared.data.common.utilities.combine(
        isAmountError.stream,
        isDescriptionError.stream,
        isCustomerError.stream,
        isTransactionDateError.stream,
        isLoading.stream
    ) { amountError, descriptionError, customerError, dateError, loading ->
        !loading && !amountError && !descriptionError && !customerError && !dateError
    }

    val actionEnabled = DerivedState(false, actionEnabledStream)

    fun fromProjectIncome(income: ProjectIncome) {
        amount.update(income.amount.toString())
        description.update(income.description)
        customer.update(income.customer)
        customerText.update(income.customer.name)
        transactionDate.update(income.transactionDate)
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProjectIncomeSheet(
    project: Project?,
    income: ProjectIncome? = null,
    customers: List<Customer> = emptyList(),
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    val scope = rememberCoroutineScope()
    val state = remember { StateHolder() }

    LaunchedEffect(Unit) {
        if (income != null) {
            state.fromProjectIncome(income)
        } else {
            // Set default date to today
            state.transactionDate.update(Clock.System.now().toString().substring(0, 10))
        }
    }

    AppBottomSheet(onDismissRequest = onDismissRequest) {
        AppLazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            SheetContent(
                state = state,
                project = project,
                income = income,
                customers = customers,
                onDismissRequest = onDismissRequest,
                onFinish = onFinish
            )
        }
    }
}

private fun LazyListScope.SheetContent(
    state: StateHolder,
    project: Project?,
    income: ProjectIncome?,
    customers: List<Customer>,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    item {
        val title = if (income != null) "تعديل إيراد" else "إضافة إيراد"
        AppSectionTitleText(title)
    }

    item { ActionsRow(state = state, project = project, income = income, onDismissRequest = onDismissRequest, onFinish = onFinish) }

    item { AmountInput(state = state) }

    item { DescriptionInput(state = state) }

    item { CustomerInput(state = state, customers = customers) }

    item { TransactionDateInput(state = state) }
}

@Composable
private fun AmountInput(state: StateHolder) {
    AppMoneyTextField(
        modifier = Modifier.fillMaxWidth(),
        money = state.amount.value,
        hint = "المبلغ",
        enabled = !state.isLoading.value,
        isError = state.isAmountError.value,
        onValueChange = { state.amount.update(it) },
        errorText = "المبلغ يجب أن يكون أكبر من صفر"
    )
}

@Composable
private fun DescriptionInput(state: StateHolder) {
    AppTextField(
        modifier = Modifier.fillMaxWidth(),
        text = state.description.value,
        hint = "الوصف",
        enabled = !state.isLoading.value,
        isError = state.isDescriptionError.value,
        onValueChange = { state.description.update(it) },
        errorText = "الوصف يجب أن يكون على الأقل 3 أحرف"
    )
}

@Composable
private fun CustomerInput(state: StateHolder, customers: List<Customer>) {
    AppSelectableAutoCompleteTextField(
        modifier = Modifier.fillMaxWidth(),
        items = customers,
        text = state.customerText.value,
        onValueChange = { state.customerText.update(it) },
        hint = "العميل",
        enabled = !state.isLoading.value,
        isError = state.isCustomerError.value,
        errorText = "يجب اختيار عميل",
        selected = state.customer.value,
        onItemClick = { 
            state.customer.update(it)
            state.customerText.update(it.name)
        },
        displayMapper = { it.name },
        searchMapper = { it.name },
        onCancelSelected = { 
            state.customer.update(null)
            state.customerText.update("")
        },
        isCancelSelectedEnabled = true,
        onEmptyItemClick = null
    )
}

@Composable
private fun TransactionDateInput(state: StateHolder) {
    AppDateTextField(
        modifier = Modifier.fillMaxWidth(),
        date = state.transactionDate.value,
        hint = "تاريخ المعاملة",
        enabled = !state.isLoading.value,
        isError = state.isTransactionDateError.value,
        onValueChange = { state.transactionDate.update(it) },
        errorText = "يجب إدخال تاريخ صحيح"
    )
}

@Composable
private fun ActionsRow(
    modifier: Modifier = Modifier,
    state: StateHolder,
    project: Project?,
    income: ProjectIncome?,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    val scope = rememberCoroutineScope()

    AppRow(
        modifier = modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
        alignment = Alignment.CenterVertically
    ) {
        AppTextButton(
            modifier = Modifier,
            text = "إلغاء",
            enabled = !state.isLoading.value,
            onClick = onDismissRequest
        )

        AppTextButton(
            modifier = Modifier,
            text = if (income != null) "تعديل" else "إضافة",
            enabled = state.actionEnabled.value,
            onClick = {
                scope.launch {
                    if (income != null) {
                        updateProjectIncome(state = state, project = project, income = income, onFinish = onFinish)
                    } else {
                        addProjectIncome(state = state, project = project, onFinish = onFinish)
                    }
                    onDismissRequest()
                }
            }
        )
    }
}

private suspend fun addProjectIncome(
    state: StateHolder,
    project: Project?,
    onFinish: () -> Unit
) {
    jobWrapper(
        state = state,
        loading = "جاري إضافة الإيراد ...",
        success = "تم إضافة الإيراد",
        job = { addProjectIncomeJob(state = state, project = project, onFinish = onFinish) }
    )
}

private suspend fun addProjectIncomeJob(
    state: StateHolder,
    project: Project?,
    onFinish: () -> Unit
) {
    if (project == null) return

    TempDI.projects.addIncome(
        projectId = project.id,
        customerId = state.customer.get()!!.id,
        amount = AppDouble(state.amount.get().toDouble()),
        description = state.description.get(),
        transactionDateIseoString = state.transactionDate.get()
    )

    onFinish()
}

private suspend fun updateProjectIncome(
    state: StateHolder,
    project: Project?,
    income: ProjectIncome,
    onFinish: () -> Unit
) {
    jobWrapper(
        state = state,
        loading = "جاري تعديل الإيراد ...",
        success = "تم تعديل الإيراد",
        job = { updateProjectIncomeJob(state = state, project = project, income = income, onFinish = onFinish) }
    )
}

private suspend fun updateProjectIncomeJob(
    state: StateHolder,
    project: Project?,
    income: ProjectIncome,
    onFinish: () -> Unit
) {
    // Note: Update functionality would need to be implemented in the repository
    // For now, we'll just call onFinish
    onFinish()
}

private suspend fun <T> jobWrapper(state: StateHolder, loading: String, success: String, job: suspend () -> T): T? {
    val status = TempDI.status
    val errorHolder = TempDI.errorHandler

    return errorHolder.execute {
        state.isLoading.update(true)

        val result = status.execute(
            loading = Message.fromString(loading),
            success = Message.fromString(success),
            job = job
        )

        state.isLoading.update(false)

        return@execute if (result is AsyncResult.Fail) throw result.error else result.result
    }
}
