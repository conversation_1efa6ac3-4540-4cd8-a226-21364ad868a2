package com.ryinex.accountant.client.shared.presentation.beneficiaries.presentation.ui

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.ModalBottomSheetDefaults
import androidx.compose.material3.SheetValue
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppButton
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppCard
import com.ryinex.accountant.client.shared.presentation.common.containers.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.containers.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.containers.AppRow
import com.ryinex.accountant.client.shared.presentation.common.containers.ForceLtr
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.images.AppSupportImageViewButton
import com.ryinex.accountant.client.shared.presentation.common.texts.AppDecimalTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabelText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledLabelTextHorizontal
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSelectableAutoCompleteTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTitleText
import com.ryinex.accountant.client.shared.data.beneficiaries.repositories.BeneficiariesTransactionsRepository
import com.ryinex.accountant.client.shared.data.projects.models.ProjectExpense
import com.ryinex.accountant.client.shared.data.projects.repositories.ProjectsRepository
import com.ryinex.accountant.client.shared.data.terms.models.Term
import com.ryinex.accountant.client.shared.data.terms.models.TermsGroup
import com.ryinex.accountant.client.shared.data.common.error.ErrorHandler
import com.ryinex.accountant.client.shared.data.common.utilities.AppDouble
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI.terms
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI.termsGroups
import com.ryinex.accountant.client.shared.data.common.utilities.numberFormat
import com.ryinex.accountant.client.shared.presentation.common.DateTimePicker
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppDelayedConfirmButton
import com.ryinex.accountant.client.shared.presentation.common.shadowBluish
import com.ryinex.accountant.client.shared.presentation.common.surface.AppBottomSheet
import com.ryinex.accountant.client.shared.presentation.projects.presentation.ui.ProjectExpenseSheet
import com.ryinex.accountant.client.shared.presentation.terms.AddTermSheet
import com.ryinex.kotlin.datatable.data.DataTable
import com.ryinex.kotlin.datatable.data.DataTableConfig
import com.ryinex.kotlin.datatable.data.composable
import com.ryinex.kotlin.datatable.data.text
import com.ryinex.kotlin.datatable.views.EmbeddedDataTableView
import core.common.extensions.dateFormat
import core.common.extensions.dateTime
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import kotlinx.coroutines.launch
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toInstant
import kotlinx.datetime.toLocalDateTime

internal fun LazyListScope.BeneficiaryTransactionsListSection(
    table: DataTable<ProjectExpense>,
    transactions: List<ProjectExpense>,
    isEditEnabled: Boolean,
    isPaymentEnabled: Boolean = false,
    isDesktop: Boolean,
    scrollState: ScrollState,
    onRefresh: () -> Unit
) {
    if (isDesktop) {

        EmbeddedDataTableView(scrollState, table)
    } else {
        transactions.forEach { item ->
            item {
                var showEditSheet by remember { mutableStateOf(false) }
                var expense by remember { mutableStateOf<ProjectExpense?>(null) }
                BeneficiaryTransactionCard(
                    expense = item,
                    isEditEnabled = isEditEnabled,
                    isPaymentEnabled = isPaymentEnabled,
                    onFinishEdit = onRefresh
                )

                if (showEditSheet && expense != null) {
                    ProjectExpenseSheet(
                        project = expense!!.project,
                        expense = expense,
                        onDismissRequest = { showEditSheet = false },
                        onFinish = {
                            showEditSheet = false
                            onRefresh()
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun BeneficiaryTransactionCard(
    expense: ProjectExpense,
    isEditEnabled: Boolean,
    isPaymentEnabled: Boolean,
    onFinishEdit: () -> Unit,
    modifier: Modifier = Modifier
) {
    AppCard(
        modifier = modifier.fillMaxWidth().shadowBluish()
    ) {
        var extended by remember { mutableStateOf(false) }


        AppColumn(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { extended = !extended }
                .padding(Dimensions.Paddings.medium.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
            alignment = Alignment.Start
        ) {
            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.SpaceBetween,
                alignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier.fillMaxWidth().weight(1f),
                    contentAlignment = if (LayoutDirection.Rtl == LocalLayoutDirection.current) Alignment.CenterStart else Alignment.CenterEnd
                ) {
                    ForceLtr {
                        AppTitleText(
                            text = expense.beneficiaryTransaction.amount.formatted(),
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }

                AppColumn(
                    arrangement = Arrangement.Top,
                    alignment = Alignment.End
                ) {
                    var showEditSheet by remember { mutableStateOf(false) }

                    if (isPaymentEnabled && expense.beneficiaryTransaction.amountPaid < expense.beneficiaryTransaction.amount) {
                        AppTextButton(
                            modifier = Modifier,
                            text = "تحصيل",
                            enabled = isPaymentEnabled,
                            onClick = {}
                        )
                    }

                    AppRow(
                        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                        alignment = Alignment.CenterVertically
                    ) {
                        if (isEditEnabled) {
                            AppSupportImageViewButton(
                                painter = rememberVectorPainter(Icons.Default.Edit),
                                enabled = isEditEnabled,
                                onClick = { showEditSheet = true },
                            )
                        }

                        AppSupportImageViewButton(
                            painter = rememberVectorPainter(if (extended) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown),
                            enabled = true,
                            onClick = { extended = !extended },
                            backgroundColor = Color.Transparent,
                            tint = MaterialTheme.colorScheme.tertiary
                        )
                    }

                    if (showEditSheet) {
                        ProjectExpenseSheet(
                            project = expense.project,
                            expense = expense,
                            onDismissRequest = { showEditSheet = false },
                            onFinish = {
                                showEditSheet = false
                                onFinishEdit()
                            }
                        )
                    }
                }

            }

            AppColumn(
                modifier = Modifier,
                arrangement = Arrangement.Top,
                alignment = Alignment.Start
            ) {
                if (expense.beneficiaryTransaction.description.isNotBlank()) {
                    AppLabelText(
                        text = expense.beneficiaryTransaction.description,
                        color = LocalContentColor.current
                    )
                } else {
                    AppLabeledLabelTextHorizontal(
                        label = "البند",
                        text = expense.term?.name ?: ""
                    )
                }

                if (expense.beneficiaryTransaction.amountPaid < expense.beneficiaryTransaction.amount) {
                    AppLabeledLabelTextHorizontal(
                        label = "تم تحصيل",
                        text = expense.beneficiaryTransaction.amountPaid.formatted()
                    )
                }

                if (extended) {
                    AppLabeledLabelTextHorizontal(
                        label = "المستفيد",
                        text = expense.beneficiary?.name ?: ""
                    )
                    AppLabeledLabelTextHorizontal(
                        label = "المشروع",
                        text = expense.project.name
                    )
                    if (expense.beneficiaryTransaction.description.isNotBlank()) {
                        AppLabeledLabelTextHorizontal(
                            label = "البند",
                            text = expense.term?.name ?: ""
                        )
                    }
                    AppLabeledLabelTextHorizontal(
                        label = "المجموعة",
                        text = expense.termsGroup?.name ?: ""
                    )

                    AppLabeledLabelTextHorizontal(
                        label = "بواسطة",
                        text = expense.beneficiaryTransaction.createdByUser.name
                    )

                    AppLabeledLabelTextHorizontal(
                        label = "بتاريخ",
                        text = expense.beneficiaryTransaction.transactionDate.dateFormat(),
                        ltrText = true
                    )
                }
            }
        }
    }
}