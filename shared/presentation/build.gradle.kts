plugins {
    id("client.common.module")
    id("client.android.target.library")
    id("client.jvm.target.library")
    id("client.js.target.library")
    id("client.config.values")
    alias(libs.plugins.jetbrainsCompose)
    alias(libs.plugins.compose.compiler)
}

kotlin {

    sourceSets {
        commonMain.dependencies {
            implementation(project(":core-common"))
            implementation(project(":core-voice"))
            implementation(project(":core-monitoring-common"))
            implementation(project(":datetime-wheel-picker"))

            implementation(compose.runtime)
            implementation(compose.foundation)
            implementation("org.jetbrains.compose.material3:material3:1.8.0-alpha03")
            implementation("org.jetbrains.compose.material:material-icons-core:1.6.11")
            implementation(compose.ui)
            implementation(compose.components.resources)
            implementation(compose.components.uiToolingPreview)

            implementation(projects.sharedData)
            implementation(projects.sharedDomain)

            implementation(libs.compose.data.table)
            implementation(libs.voyager.navigator)
            implementation(libs.voyager.screenmodel)
        }
    }
}

compose.resources {
    packageOfResClass = "com.ryinex.accountant.client.resources"
}

jsDependencies {
    implementation("org.jetbrains.kotlinx:kotlinx-browser:0.2")
}