[versions]
androidDesugarJdk = "1.1.5"
androidGradlePlugin = "8.0.1"
androidMaterial = "1.9.0"
androidSdkCompile = "35"
androidSdkMin = "24"
androidSdkTarget = "33"

androidxAnnotation = "1.6.0"
androidxAppcombat = "1.6.1"
androidxBrowser = "1.5.0"
androidxCore = "1.9.0"
androidxCoreSplashScreen = "1.1.0-alpha02"
androidxLifecycle = "2.6.1"
androidxNavigation = "2.5.3"
androidxTestCoreKtx = "1.5.0"
androidxTestRules = "1.5.0"
androidxTestRunner = "1.5.2"
androidxWork = "2.8.1"

androidxEspresso = "3.5.1"
androidxTestJunit = "1.1.5"

composeBom = "2023.05.01"
composeActivity = "1.6.1"
composeCompiler = "1.4.7"

crashkios = "0.8.2"

daggerHilt = "2.46.1"

firebaseBom = "32.3.1"
firebaseCrashlyticsGradlePlugin = "2.9.4"
firebasePerformanceGradlePlugin = "1.4.2"

glide = "4.15.1"

googlePlayAppUpdate = "2.1.0"
googlePlayCoroutines = "1.7.2"
googlePlayStoreReview = "2.0.1"
playServices = "21.0.1"
googlePlayServicesGradlePlugin = "4.4.0"

gson = "2.8.5"

huaweiConnectGradlePlugin = "1.8.1.300"

javaCompatibility = "19"
javaSource = "19"
javaTarget = "19"

jetbrainsCompose = "1.8.1"

junit = "4.13.2"

koin = "4.0.3"
koinAndroid = "4.0.3"
koinKsp = "2.0.0"

#kotlin = "2.1.0-Beta2"
kotlin = "2.1.0"
googleKsp = "2.1.20-1.0.31" # this refernces kotlin version
nativeCoroutines = "1.0.0-ALPHA-9" # this refernces kotlin version and google ksp and coroutines

kotlinxCoroutines = "1.10.2"
kotlinxDateTime = "0.6.1"
kotlinxSerialization = "1.8.0"

koverGradlePlugin = "0.7.0-Alpha"

ktlintGradlePlugin = "11.3.1"

ktor = "3.1.2"

lottieAnimation = "6.0.0"

mokoResources = "0.23.0"
mokoGraphics = "0.9.0"

multiplatformUuid = "0.8.4"

nordicSemiconductor = "2.3.0"
nsExceptionKt = "0.1.7"

okhttp = "5.0.0-alpha.2"

retrofit = "2.9.0"

sqldelight = "2.0.2"
voyager = "1.1.0-beta03"
zxingAndroidEmbedded = "4.3.0"

[libraries]
android-desugar-jdk = { group = "coid.tools", name = "desugar_jdk_libs", version = "androidDesugarJdk" }
android-gradle-plugin = { group = "com.android.tools.build", name = "gradle", version.ref = "androidGradlePlugin"}
android-material = { group = "com.google.android.material", name = "material", version.ref = "androidMaterial" }

androidx-annotation = { group = "androidx.annotation", name = "annotation", version.ref = "androidxAnnotation" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "androidxAppcombat" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "androidxCore" }
androidx-core-splashscreen = { group = "androidx.core", name = "core-splashscreen", version.ref = "androidxCoreSplashScreen" }
androidx-test-core-ktx = { group = "androidx.test", name = "core-ktx", version.ref = "androidxTestCoreKtx" }
androidx-test-rules = { group = "androidx.test", name = "rules", version.ref = "androidxTestRules" }
androidx-test-runner = { group = "androidx.test", name = "runner", version.ref ="androidxTestRunner" }

androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "androidxEspresso" }
androidx-test-junit = { group = "androidx.test.ext", name = "junit", version.ref = "androidxTestJunit" }

androidx-browser =    { group = "androidx.browser", name = "browser", version.ref = "androidxBrowser"}

androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-compose-foundation = { group = "androidx.compose.foundation", name = "foundation" }
androidx-compose-material3 = { group = "androidx.compose.material3", name = "material3" }
androidx-compose-material-icons-extended = { group = "androidx.compose.material", name = "material-icons-extended" }
androidx-compose-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-compose-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-compose-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-compose-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-compose-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-compose-activity = { group = "androidx.activity", name = "activity-compose", version.ref = "composeActivity" }
androidx-compose-lifecycle = { group = "androidx.lifecycle", name = "lifecycle-runtime-compose", version.ref = "androidxLifecycle" }
androidx-compose-navigation = { group = "androidx.navigation", name = "navigation-compose", version.ref = "androidxNavigation" }
# androidx-lifecycle-compiler = { group = "androidx.lifecycle", name = "lifecycle-compiler", version.ref = "androidxLifecycle" }
androidx-lifecycle-process = { group = "androidx.lifecycle", name = "lifecycle-process", version.ref = "androidxLifecycle" }
androidx-lifecycle-runtime = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "androidxLifecycle" }
androidx-lifecycle-runtime-testing = { group = "androidx.lifecycle", name = "lifecycle-runtime-testing", version.ref = "androidxLifecycle" }
androidx-lifecycle-service = { group = "androidx.lifecycle", name = "lifecycle-service", version.ref = "androidxLifecycle" }
androidx-lifecycle-viewmodel = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "androidxLifecycle" }

androidx-navigation-fragment = { group = "androidx.navigation", name = "navigation-fragment-ktx", version.ref = "androidxNavigation" }
androidx-navigation-runtime = { group = "androidx.navigation", name = "navigation-runtime-ktx", version.ref = "androidxNavigation" }
androidx-navigation-ui = { group = "androidx.navigation", name = "navigation-ui-ktx", version.ref = "androidxNavigation" }
androidx-navigation-test = { group = "androidx.navigation", name = "navigation-testing", version.ref = "androidxNavigation" }

androidx-work-runtime = { group = "androidx.work", name = "work-runtime-ktx", version.ref = "androidxWork" }
androidx-work-testing = { group = "androidx.work", name = "work-testing", version.ref = "androidxWork" }

crashkios = { group = "co.touchlab.crashkios", name = "crashlytics", version.ref = "crashkios" }

dagger-hilt-android = { group = "com.google.dagger.hilt", name = "hilt-android", version.ref = "daggerHilt" }
dagger-hilt-kapt = { group = "com.google.dagger", name = "hilt-compiler", version.ref = "daggerHilt" }

retrofit = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofit" }
retrofit-gson = { group = "com.squareup.retrofit2", name = "converter-gson", version.ref = "retrofit" }

firebase-bom = { group = "com.google.firebase", name = "firebase-bom", version.ref = "firebaseBom" }
firebase-crashlytics-gradle-plugin  = { group = "com.google.firebase", name = "firebase-crashlytics-gradle", version.ref = "firebaseCrashlyticsGradlePlugin" }
firebase-cloud-messaging = { group = "com.google.firebase", name = "firebase-messaging-ktx" }
firebase-performance-gradle-plugin = { group = "com.google.firebase", name = "perf-plugin", version.ref = "firebasePerformanceGradlePlugin" }

glide = { group = "com.github.bumptech.glide", name = "glide", version.ref = "glide" }
glide-compiler = { group = "com.github.bumptech.glide", name = "compiler", version.ref = "glide" }

google-play-app-update = { group = "com.google.android.play", name = "app-update-ktx", version.ref = "googlePlayAppUpdate" }
google-play-coroutines = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-play-services", version.ref = "googlePlayCoroutines" }
google-play-review-kts = { group = "com.google.android.play", name = "review-ktx", version.ref = "googlePlayStoreReview" }
google-play-services-gradle-plugin = { group = "com.google.gms", name = "google-services", version.ref = "googlePlayServicesGradlePlugin" }
google-play-services-location = { group = "com.google.android.gms", name = "play-services-location", version.ref = "playServices" }
google-play-services-maps = { group = "com.google.android.gms", name = "play-services-maps", version = "18.0.1" }

gson = { group = "com.google.code.gson", name = "gson", version.ref = "gson" }

huawei-connect-gradle-plugin = { group = "com.huawei.agconnect", name = "agcp", version.ref = "huaweiConnectGradlePlugin" }

jetbrains-compose-gradle-plugin = { group = "org.jetbrains.compose", name = "compose-gradle-plugin", version.ref = "jetbrainsCompose" }

junit = { group = "junit", name = "junit", version.ref = "junit" }

koin-annotations = { group = "io.insert-koin", name = "koin-annotations", version.ref = "koinKsp" }
koin-ksp-compiler = { group = "io.insert-koin", name = "koin-ksp-compiler", version.ref = "koinKsp" }

koin-core = { group = "io.insert-koin", name = "koin-core", version.ref = "koin" }
koin-core-coroutines = { group = "io.insert-koin", name = "koin-core-coroutines", version.ref = "koin" }
koin-test = { group = "io.insert-koin", name = "koin-test", version.ref = "koin" }

koin-android = { group = "io.insert-koin", name = "koin-android", version.ref = "koinAndroid" }
koin-android-compat = { group = "io.insert-koin", name = "koin-android-compat", version.ref = "koinAndroid" }
koin-android-test = { group = "io.insert-koin", name = "koin-android-test", version = "3.4.0" }
koin-androidx-navigation = { group = "io.insert-koin", name = "koin-androidx-navigation", version.ref = "koinAndroid" }
koin-androidx-workmanager = { group = "io.insert-koin", name = "koin-androidx-workmanager", version.ref = "koinAndroid" }

kotlin-gradle-plugin = { group = "org.jetbrains.kotlin", name = "kotlin-gradle-plugin", version.ref = "kotlin" }
kotlin-reflect = { group = "org.jetbrains.kotlin", name = "kotlin-reflect", version.ref = "kotlin" }
kotlin-serialization-gradle-plugin = { group = "org.jetbrains.kotlin", name = "kotlin-serialization", version.ref = "kotlin" }
kotlin-test = { group = "org.jetbrains.kotlin", name = "kotlin-test", version.ref = "kotlin" }

kotlinx-coroutines-android = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-android", version.ref = "kotlinxCoroutines" }
kotlinx-coroutines-core = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-core", version.ref = "kotlinxCoroutines" }
kotlinx-coroutines-test = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-test", version.ref = "kotlinxCoroutines" }

kotlinx-datetime = { group = "org.jetbrains.kotlinx", name = "kotlinx-datetime", version.ref = "kotlinxDateTime" }
kotlinx-serialization-json = { group = "org.jetbrains.kotlinx", name = "kotlinx-serialization-json", version.ref = "kotlinxSerialization" }

ktor-client-android = { group = "io.ktor", name = "ktor-client-android", version.ref = "ktor" }
ktor-client-core = { group = "io.ktor", name = "ktor-client-core", version.ref = "ktor" }
ktor-client-darwin = { group = "io.ktor", name = "ktor-client-darwin", version.ref = "ktor" }
ktor-client-mock = { group = "io.ktor", name = "ktor-client-mock", version.ref = "ktor" }
ktor-client-js = { group = "io.ktor", name = "ktor-client-js", version.ref = "ktor" }
ktor-client-okhttp = { group = "io.ktor", name = "ktor-client-okhttp", version.ref = "ktor" }
ktor-client-content-negotiation = { group = "io.ktor", name = "ktor-client-content-negotiation", version.ref = "ktor" }
ktor-client-encoding = { group = "io.ktor", name = "ktor-client-encoding", version.ref = "ktor" }
ktor-serialization-kotlinx-json = { group = "io.ktor", name = "ktor-serialization-kotlinx-json", version.ref = "ktor" }

lottie-animation = { group = "com.airbnb.android", name = "lottie-compose", version.ref = "lottieAnimation" }

moko-graphics = { group = "dev.icerock.moko", name = "graphics", version.ref = "mokoGraphics" }
moko-resources = { group = "dev.icerock.moko", name = "resources", version.ref = "mokoResources" }
moko-resources-compose = { group = "dev.icerock.moko", name = "resources-compose", version.ref = "mokoResources" }
moko-resources-gradle-plugin = { group = "dev.icerock.moko", name = "resources-generator", version.ref = "mokoResources" }
moko-resources-test = { group = "dev.icerock.moko", name = "resources-test", version.ref = "mokoResources" }

multiplatform-uuid = { group = "com.benasher44", name = "uuid", version.ref = "multiplatformUuid" }

nordic-dfu = { group = "no.nordicsemi.android", name = "dfu", version.ref = "nordicSemiconductor" }
nordic-navigation = { group = "no.nordicsemi.android.common", name = "navigation", version.ref = "nordicSemiconductor" }
nordic-theme = { group = "no.nordicsemi.android.common", name = "theme", version.ref = "nordicSemiconductor" }
nordic-ui-logger = { group = "no.nordicsemi.android.common", name = "uilogger", version.ref = "nordicSemiconductor" }

nsexception-kt = { group = "com.rickclephas.kmp", name = "nsexception-kt-crashlytics", version.ref = "nsExceptionKt" }

okhttp-logging-interceptor = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okhttp" }

sqldelight-android-driver = { group = "app.cash.sqldelight", name = "android-driver", version.ref = "sqldelight" }
sqldelight-gradle-plugin = { group = "app.cash.sqldelight", name = "gradle-plugin", version.ref = "sqldelight" }
sqldelight-runtime = { group = "app.cash.sqldelight", name = "runtime", version.ref = "sqldelight" }
sqldelight-sqlite-driver = { group = "app.cash.sqldelight", name = "sqlite-driver", version.ref = "sqldelight" }
sqldelight-primitive-adapters = { group = "app.cash.sqldelight", name = "primitive-adapters", version.ref = "sqldelight" }
sqldelight-coroutines = { group = "app.cash.sqldelight", name = "coroutines-extensions", version.ref = "sqldelight" }

voyager-navigator = { module = "cafe.adriel.voyager:voyager-navigator", version.ref = "voyager" }
voyager-screenModel = { module = "cafe.adriel.voyager:voyager-screenmodel", version.ref = "voyager" }
voyager-bottomSheetNavigator = { module = "cafe.adriel.voyager:voyager-bottom-sheet-navigator", version.ref = "voyager" }
voyager-tabNavigator = { module = "cafe.adriel.voyager:voyager-tab-navigator", version.ref = "voyager" }
voyager-transitions = { module = "cafe.adriel.voyager:voyager-transitions", version.ref = "voyager" }
voyager-koin = { module = "cafe.adriel.voyager:voyager-koin", version.ref = "voyager" }
voyager-hilt = { module = "cafe.adriel.voyager:voyager-hilt", version.ref = "voyager" }
voyager-kodein = { module = "cafe.adriel.voyager:voyager-kodein", version.ref = "voyager" }
voyager-rxjava = { module = "cafe.adriel.voyager:voyager-rxjava", version.ref = "voyager" }

zxing-android-embedded = { group = "com.journeyapps", name = "zxing-android-embedded", version.ref = "zxingAndroidEmbedded" }

[plugins]
android-application = { id = "com.android.application", version.ref = "androidGradlePlugin" }
android-library = { id = "com.android.library", version.ref = "androidGradlePlugin" }

crashlytics-link = { id = "co.touchlab.crashkios.crashlyticslink", version.ref = "crashkios" }

dagger-hilt = { id = "com.google.dagger.hilt.android", version.ref = "daggerHilt" }

firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "firebaseCrashlyticsGradlePlugin" }
firebase-performance = { id = "com.google.firebase.firebase-perf", version.ref = "firebasePerformanceGradlePlugin" }

google-ksp = { id = "com.google.devtools.ksp", version.ref = "googleKsp" }
google-play-services = { id = "com.google.gms.google-services", version.ref = "googlePlayServicesGradlePlugin" }

huawei-connect = { id = "com.huawei.agconnect", version.ref = "huaweiConnectGradlePlugin" }

jetbrains-compose = { id = "org.jetbrains.compose", version.ref = "jetbrainsCompose" }

kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kotlin-kapt = { id = "org.jetbrains.kotlin.kapt", version.ref = "kotlin" }
kotlin-multiplatform = { id = "org.jetbrains.kotlin.multiplatform", version.ref = "kotlin" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }

kover-gradle = { id = "org.jetbrains.kotlinx.kover", version.ref = "koverGradlePlugin" }

ktlint-gradle = { id = "org.jlleitschuh.gradle.ktlint", version.ref = "ktlintGradlePlugin" }

moko-resources = { id = "dev.icerock.mobile.multiplatform-resources", version.ref = "mokoResources" }

native-coroutines = { id = "com.rickclephas.kmp.nativecoroutines", version.ref = "nativeCoroutines" }


sqldelight = { id = "app.cash.sqldelight", version.ref = "sqldelight" }

# @formatter:on
